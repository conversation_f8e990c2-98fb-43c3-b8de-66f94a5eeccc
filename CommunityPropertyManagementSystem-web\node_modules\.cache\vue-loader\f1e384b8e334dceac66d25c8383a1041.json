{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue?vue&type=style&index=0&id=a44c444e&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749136123922}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749133880608}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749133882085}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749133881164}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue"], "names": [], "mappings": ";AA+LA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,EAAE,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACzC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;AACF", "file": "I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/views/admin/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div class=\"welcome-section\">\r\n      <h2 class=\"welcome-title\">欢迎使用小区物业管理系统</h2>\r\n      <p class=\"welcome-info\">\r\n        账号：<b style=\"color: #409EFF;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #409EFF;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片区域 -->\r\n    <div class=\"statistics-container\">\r\n      <div class=\"row\">\r\n        <!-- 管理员统计卡片 -->\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card staff-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-person-badge-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.staffCount || 0 }}</h3>\r\n              <p>工作人员数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 工作人员统计卡片 -->\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card announcement-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-megaphone-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.announcementCount || 0 }}</h3>\r\n              <p>公告信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户统计卡片 -->\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card house-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-house-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.houseCount || 0 }}</h3>\r\n              <p>房屋信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      statistics: {},\r\n      loading: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        let url = \"\";\r\n        let params = {};\r\n\r\n        if (this.role === \"管理员\") {\r\n          url = base + \"/statistics/admin\";\r\n        } else if (this.role === \"工作人员\") {\r\n          url = base + \"/statistics/staff\";\r\n        } else if (this.role === \"用户\") {\r\n          url = base + \"/statistics/usertotal\";\r\n          // 获取当前用户ID\r\n          const user = JSON.parse(sessionStorage.getItem(\"user\") || \"{}\");\r\n          params.userId = user.uid;\r\n        }\r\n\r\n        if (url) {\r\n          const response = await request.post(url, params);\r\n          if (response.code === 200) {\r\n            this.statistics = response.resdata;\r\n          } else {\r\n            this.$message({\r\n              message: response.msg || \"获取统计数据失败\",\r\n              type: \"error\",\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取统计数据失败:\", error);\r\n        this.$message({\r\n          message: \"获取统计数据失败\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n#home {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.welcome-section {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 30px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 15px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 2.5rem;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.welcome-info {\r\n  font-size: 1.2rem;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.statistics-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n  padding: 15px;\r\n  flex: 1;\r\n  min-width: 280px;\r\n}\r\n\r\n.col-md-4 {\r\n  flex: 0 0 33.333333%;\r\n}\r\n\r\n.col-md-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.col-lg-3 {\r\n  flex: 0 0 25%;\r\n}\r\n\r\n.col-lg-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 120px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));\r\n}\r\n\r\n.stat-icon {\r\n  font-size: 3rem;\r\n  margin-right: 20px;\r\n  color: var(--card-color);\r\n  opacity: 0.8;\r\n}\r\n\r\n.stat-content h3 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 5px 0;\r\n  color: #2c3e50;\r\n}\r\n\r\n.stat-content p {\r\n  font-size: 1rem;\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 不同卡片的颜色主题 */\r\n.user-card {\r\n  --card-color: #3498db;\r\n  --card-color-light: #5dade2;\r\n}\r\n\r\n.staff-card {\r\n  --card-color: #2ecc71;\r\n  --card-color-light: #58d68d;\r\n}\r\n\r\n.announcement-card {\r\n  --card-color: #f39c12;\r\n  --card-color-light: #f8c471;\r\n}\r\n\r\n.complaint-card {\r\n  --card-color: #e74c3c;\r\n  --card-color-light: #ec7063;\r\n}\r\n\r\n.expense-card {\r\n  --card-color: #9b59b6;\r\n  --card-color-light: #bb8fce;\r\n}\r\n\r\n.house-card {\r\n  --card-color: #1abc9c;\r\n  --card-color-light: #5dccb4;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n    flex: 0 0 100%;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .stat-card {\r\n    height: auto;\r\n    min-height: 100px;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    font-size: 2.5rem;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stat-content h3 {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  #home {\r\n    padding: 10px;\r\n  }\r\n\r\n  .welcome-section {\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .welcome-info {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    height: auto;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}