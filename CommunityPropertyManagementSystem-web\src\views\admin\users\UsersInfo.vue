﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="用户名" prop="ulname">
<el-input v-model="formData.ulname" placeholder="用户名"  style="width:50%;" disabled ></el-input>
</el-form-item>
<el-form-item label="姓名" prop="uname">
<el-input v-model="formData.uname" placeholder="姓名"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="性别" prop="gender">
<el-radio-group v-model="formData.gender">
<el-radio label="男">
男
</el-radio>
<el-radio label="女">
女
</el-radio>
</el-radio-group>
</el-form-item>
<el-form-item label="联系方式" prop="contact">
<el-input v-model="formData.contact" placeholder="联系方式"  style="width:50%;" ></el-input>
</el-form-item>

<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'UsersInfo',
  components: {
    
  },  
    data() {
      return {   
        id: '',
        isClear: false,
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          uname: [{ required: true, message: '请输入姓名', trigger: 'blur' },
],          gender: [{ required: true, message: '请输入性别', trigger: 'blur' },
],          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },
],          uflag: [{ required: true, message: '请输入审核状态', trigger: 'blur' },
],        },

      };
    },
     created() {
        var user = JSON.parse(sessionStorage.getItem("user"));
        this.id = user.uid;
        this.getDatas();
      }, 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/users/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
            
            
          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/users/update";
              this.btnLoading = true;
              
              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });                 
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
       
              
          
           
           
      },
}

</script>
<style scoped>
</style>
 

