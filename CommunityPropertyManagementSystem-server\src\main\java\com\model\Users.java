package com.model;
import java.util.List;

/**
* (users)用户实体类
*/
public class Users extends ComData{
	
	private static final long serialVersionUID = 447184277318358L;
	private Integer uid;    //用户id
	private String ulname;    //用户名
	private String loginpassword;    //登录密码
	private String uname;    //姓名
	private String gender;    //性别
	private String contact;    //联系方式
	private String registertime;    //注册时间
	private String uflag;    //审核状态

	public Integer getUid() {
		return uid;
	}

	public void setUid(Integer uid) {
		this.uid = uid;
	}

	public String getUlname() {
		return ulname;
	}

	public void setUlname(String ulname) {
		this.ulname = ulname;
	}

	public String getLoginpassword() {
		return loginpassword;
	}

	public void setLoginpassword(String loginpassword) {
		this.loginpassword = loginpassword;
	}

	public String getUname() {
		return uname;
	}

	public void setUname(String uname) {
		this.uname = uname;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getRegistertime() {
		return registertime;
	}

	public void setRegistertime(String registertime) {
		this.registertime = registertime;
	}

	public String getUflag() {
		return uflag;
	}

	public void setUflag(String uflag) {
		this.uflag = uflag;
	}

}

