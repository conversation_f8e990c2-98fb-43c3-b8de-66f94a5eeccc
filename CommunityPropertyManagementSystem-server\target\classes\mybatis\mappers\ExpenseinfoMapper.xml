<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.ExpenseinfoMapper">
	<select id="findExpenseinfoList"  resultType="Expenseinfo">
		select * from expenseinfo 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Expenseinfo">
	    select  *  
        from expenseinfo a  	
		<where>
      		<if test="eid != null and eid !=0 ">
		    and a.eid = #{eid}
		</if>
		<if test="etitle != null and etitle != ''">
		    and a.etitle = #{etitle}
		</if>
		<if test="hno != null and hno != ''">
		    and a.hno = #{hno}
		</if>
		<if test="etime != null and etime != ''">
		    and a.etime = #{etime}
		</if>
		<if test="epaymentstatus != null and epaymentstatus != ''">
		    and a.epaymentstatus = #{epaymentstatus}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} eid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from expenseinfo a  
		<where>
      		<if test="eid != null and eid !=0 ">
		    and a.eid = #{eid}
		</if>
		<if test="etitle != null and etitle != ''">
		    and a.etitle = #{etitle}
		</if>
		<if test="hno != null and hno != ''">
		    and a.hno = #{hno}
		</if>
		<if test="etime != null and etime != ''">
		    and a.etime = #{etime}
		</if>
		<if test="epaymentstatus != null and epaymentstatus != ''">
		    and a.epaymentstatus = #{epaymentstatus}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryExpenseinfoById" parameterType="int" resultType="Expenseinfo">
    select  *  
     from expenseinfo a  	 where a.eid=#{value}
  </select>
 
	<insert id="insertExpenseinfo" useGeneratedKeys="true" keyProperty="eid" parameterType="Expenseinfo">
    insert into expenseinfo
    (etitle,hno,eamount,edescription,etime,epaymentstatus)
    values
    (#{etitle},#{hno},#{eamount},#{edescription},now(),#{epaymentstatus});
  </insert>
	
	<update id="updateExpenseinfo" parameterType="Expenseinfo" >
    update expenseinfo 
    <set>
		<if test="etitle != null and etitle != ''">
		    etitle = #{etitle},
		</if>
		<if test="hno != null and hno != ''">
		    hno = #{hno},
		</if>
		<if test="eamount != null ">
		    eamount = #{eamount},
		</if>
		<if test="edescription != null and edescription != ''">
		    edescription = #{edescription},
		</if>
		<if test="etime != null and etime != ''">
		    etime = #{etime},
		</if>
		<if test="epaymentstatus != null and epaymentstatus != ''">
		    epaymentstatus = #{epaymentstatus},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="eid != null or eid != ''">
      eid=#{eid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteExpenseinfo" parameterType="int">
    delete from  expenseinfo where eid=#{value}
  </delete>

	
	
</mapper>

 
