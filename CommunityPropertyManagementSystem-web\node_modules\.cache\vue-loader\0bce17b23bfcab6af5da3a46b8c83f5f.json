{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue?vue&type=template&id=a44c444e&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749136123922}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/views/admin/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div class=\"welcome-section\">\r\n      <h2 class=\"welcome-title\">欢迎使用小区物业管理系统</h2>\r\n      <p class=\"welcome-info\">\r\n        账号：<b style=\"color: #409EFF;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #409EFF;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片区域 -->\r\n    <div class=\"statistics-container\">\r\n      <div class=\"row\">\r\n        <!-- 管理员统计卡片 -->\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card staff-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-person-badge-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.staffCount || 0 }}</h3>\r\n              <p>工作人员数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 工作人员统计卡片 -->\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card announcement-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-megaphone-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.announcementCount || 0 }}</h3>\r\n              <p>公告信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户统计卡片 -->\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card house-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-house-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.houseCount || 0 }}</h3>\r\n              <p>房屋信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      statistics: {},\r\n      loading: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        let url = \"\";\r\n        let params = {};\r\n\r\n        if (this.role === \"管理员\") {\r\n          url = base + \"/statistics/admin\";\r\n        } else if (this.role === \"工作人员\") {\r\n          url = base + \"/statistics/staff\";\r\n        } else if (this.role === \"用户\") {\r\n          url = base + \"/statistics/usertotal\";\r\n          // 获取当前用户ID\r\n          const user = JSON.parse(sessionStorage.getItem(\"user\") || \"{}\");\r\n          params.userId = user.uid;\r\n        }\r\n\r\n        if (url) {\r\n          const response = await request.post(url, params);\r\n          if (response.code === 200) {\r\n            this.statistics = response.resdata;\r\n          } else {\r\n            this.$message({\r\n              message: response.msg || \"获取统计数据失败\",\r\n              type: \"error\",\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取统计数据失败:\", error);\r\n        this.$message({\r\n          message: \"获取统计数据失败\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n#home {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.welcome-section {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 30px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 15px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 2.5rem;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.welcome-info {\r\n  font-size: 1.2rem;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.statistics-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n  padding: 15px;\r\n  flex: 1;\r\n  min-width: 280px;\r\n}\r\n\r\n.col-md-4 {\r\n  flex: 0 0 33.333333%;\r\n}\r\n\r\n.col-md-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.col-lg-3 {\r\n  flex: 0 0 25%;\r\n}\r\n\r\n.col-lg-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 120px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));\r\n}\r\n\r\n.stat-icon {\r\n  font-size: 3rem;\r\n  margin-right: 20px;\r\n  color: var(--card-color);\r\n  opacity: 0.8;\r\n}\r\n\r\n.stat-content h3 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 5px 0;\r\n  color: #2c3e50;\r\n}\r\n\r\n.stat-content p {\r\n  font-size: 1rem;\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 不同卡片的颜色主题 */\r\n.user-card {\r\n  --card-color: #3498db;\r\n  --card-color-light: #5dade2;\r\n}\r\n\r\n.staff-card {\r\n  --card-color: #2ecc71;\r\n  --card-color-light: #58d68d;\r\n}\r\n\r\n.announcement-card {\r\n  --card-color: #f39c12;\r\n  --card-color-light: #f8c471;\r\n}\r\n\r\n.complaint-card {\r\n  --card-color: #e74c3c;\r\n  --card-color-light: #ec7063;\r\n}\r\n\r\n.expense-card {\r\n  --card-color: #9b59b6;\r\n  --card-color-light: #bb8fce;\r\n}\r\n\r\n.house-card {\r\n  --card-color: #1abc9c;\r\n  --card-color-light: #5dccb4;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n    flex: 0 0 100%;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .stat-card {\r\n    height: auto;\r\n    min-height: 100px;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    font-size: 2.5rem;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stat-content h3 {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  #home {\r\n    padding: 10px;\r\n  }\r\n\r\n  .welcome-section {\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .welcome-info {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    height: auto;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}