{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749136123922}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue"], "names": [], "mappings": ";AAkIA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC/B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC;UACJ;QACF;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC;MACJ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;IACF,CAAC;EACH,CAAC;AACH,CAAC", "file": "I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/views/admin/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div class=\"welcome-section\">\r\n      <h2 class=\"welcome-title\">欢迎使用小区物业管理系统</h2>\r\n      <p class=\"welcome-info\">\r\n        账号：<b style=\"color: #409EFF;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #409EFF;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片区域 -->\r\n    <div class=\"statistics-container\">\r\n      <div class=\"row\">\r\n        <!-- 管理员统计卡片 -->\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card staff-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-person-badge-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.staffCount || 0 }}</h3>\r\n              <p>工作人员数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 工作人员统计卡片 -->\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card announcement-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-megaphone-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.announcementCount || 0 }}</h3>\r\n              <p>公告信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户统计卡片 -->\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card house-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-house-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.houseCount || 0 }}</h3>\r\n              <p>房屋信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      statistics: {},\r\n      loading: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        let url = \"\";\r\n        let params = {};\r\n\r\n        if (this.role === \"管理员\") {\r\n          url = base + \"/statistics/admin\";\r\n        } else if (this.role === \"工作人员\") {\r\n          url = base + \"/statistics/staff\";\r\n        } else if (this.role === \"用户\") {\r\n          url = base + \"/statistics/usertotal\";\r\n          // 获取当前用户ID\r\n          const user = JSON.parse(sessionStorage.getItem(\"user\") || \"{}\");\r\n          params.userId = user.uid;\r\n        }\r\n\r\n        if (url) {\r\n          const response = await request.post(url, params);\r\n          if (response.code === 200) {\r\n            this.statistics = response.resdata;\r\n          } else {\r\n            this.$message({\r\n              message: response.msg || \"获取统计数据失败\",\r\n              type: \"error\",\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取统计数据失败:\", error);\r\n        this.$message({\r\n          message: \"获取统计数据失败\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n#home {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.welcome-section {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 30px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 15px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 2.5rem;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.welcome-info {\r\n  font-size: 1.2rem;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.statistics-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n  padding: 15px;\r\n  flex: 1;\r\n  min-width: 280px;\r\n}\r\n\r\n.col-md-4 {\r\n  flex: 0 0 33.333333%;\r\n}\r\n\r\n.col-md-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.col-lg-3 {\r\n  flex: 0 0 25%;\r\n}\r\n\r\n.col-lg-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 120px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));\r\n}\r\n\r\n.stat-icon {\r\n  font-size: 3rem;\r\n  margin-right: 20px;\r\n  color: var(--card-color);\r\n  opacity: 0.8;\r\n}\r\n\r\n.stat-content h3 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 5px 0;\r\n  color: #2c3e50;\r\n}\r\n\r\n.stat-content p {\r\n  font-size: 1rem;\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 不同卡片的颜色主题 */\r\n.user-card {\r\n  --card-color: #3498db;\r\n  --card-color-light: #5dade2;\r\n}\r\n\r\n.staff-card {\r\n  --card-color: #2ecc71;\r\n  --card-color-light: #58d68d;\r\n}\r\n\r\n.announcement-card {\r\n  --card-color: #f39c12;\r\n  --card-color-light: #f8c471;\r\n}\r\n\r\n.complaint-card {\r\n  --card-color: #e74c3c;\r\n  --card-color-light: #ec7063;\r\n}\r\n\r\n.expense-card {\r\n  --card-color: #9b59b6;\r\n  --card-color-light: #bb8fce;\r\n}\r\n\r\n.house-card {\r\n  --card-color: #1abc9c;\r\n  --card-color-light: #5dccb4;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n    flex: 0 0 100%;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .stat-card {\r\n    height: auto;\r\n    min-height: 100px;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    font-size: 2.5rem;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stat-content h3 {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  #home {\r\n    padding: 10px;\r\n  }\r\n\r\n  .welcome-section {\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .welcome-info {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    height: auto;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}