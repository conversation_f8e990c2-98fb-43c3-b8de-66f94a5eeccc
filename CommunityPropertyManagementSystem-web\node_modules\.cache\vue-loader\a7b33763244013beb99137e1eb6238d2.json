{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue?vue&type=style&index=0&id=26084dc2&lang=scss&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue", "mtime": 1749134332807}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749133880608}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749133882085}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749133881164}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749133880732}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CkBpbXBvcnQgdXJsKC4uL2Fzc2V0cy9jc3MvaHRfYm9vdHN0cmFwLm1pbi5jc3MpOwpAaW1wb3J0IHVybCguLi9hc3NldHMvY3NzL2h0X2Jvb3RzdHJhcC1pY29ucy5jc3MpOwpAaW1wb3J0IHVybCguLi9hc3NldHMvY3NzL2h0X3N0eWxlLmNzcyk7Cgoucm9sZS1zZWxlY3Rpb24gewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgZ2FwOiAzMHB4OwogIHBhZGRpbmc6IDEwcHggMDsKfQoKLnJvbGUtb3B0aW9uIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiA4cHg7CiAgY3Vyc29yOiBwb2ludGVyOwp9Cgoucm9sZS1vcHRpb24gaW5wdXRbdHlwZT0ncmFkaW8nXSB7CiAgd2lkdGg6IDE4cHg7CiAgaGVpZ2h0OiAxOHB4OwogIG1hcmdpbjogMDsKICBjdXJzb3I6IHBvaW50ZXI7Cn0KCi5yb2xlLW9wdGlvbiBsYWJlbCB7CiAgbWFyZ2luOiAwOwogIGN1cnNvcjogcG9pbnRlcjsKICBmb250LXNpemU6IDFyZW07CiAgY29sb3I6ICM2ZTcwN2U7Cn0KCi5yb2xlLW9wdGlvbjpob3ZlciBsYWJlbCB7CiAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpOwp9Cg=="}, {"version": 3, "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue"], "names": [], "mappings": ";AA6aA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B", "file": "I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <div class=\"container\">\n      <div class=\"login-container\">\n        <div class=\"login-box\">\n          <div class=\"text-center mb-4\">\n            <i class=\"bi bi-shield-lock text-primary\" style=\"font-size: 3rem\"></i>\n            <h2 class=\"mt-3\">小区物业管理系统</h2>\n            <p class=\"text-muted\">请输入您的账号和密码</p>\n          </div>\n          <form id=\"loginForm\">\n            <div class=\"form-floating mb-4\">\n              <input type=\"text\" class=\"form-control\" id=\"username\" placeholder=\"用户名\" v-model=\"loginModel.username\" />\n              <label for=\"username\"><i class=\"bi bi-person me-2\"></i>用户名</label>\n            </div>\n            <div class=\"form-floating mb-4\">\n              <input type=\"password\" class=\"form-control\" id=\"password\" placeholder=\"密码\"\n                v-model=\"loginModel.password\" />\n              <label for=\"password\"><i class=\"bi bi-key me-2\"></i>密码</label>\n            </div>\n            <div class=\"mb-4\">\n              <div class=\"role-selection\">\n                <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\n                <el-radio label=\"用户\" v-model=\"loginModel.radio\">用户</el-radio>\n                <el-radio label=\"工作人员\" v-model=\"loginModel.radio\">工作人员</el-radio>\n\n              </div>\n            </div>\n\n            <button type=\"button\" class=\"btn btn-primary w-100 py-2 mb-3\" @click=\"login\">\n              <i class=\"bi bi-box-arrow-in-right me-2\"></i>登录\n            </button>\n            <div style=\"text-align: center;\">\n              <a href=\"#\" @click=\"toreg\"> 用户注册 </a>&nbsp;&nbsp;|&nbsp;&nbsp;\n\n\n              <a href=\"#\" @click=\"toreg2\"> 工作人员注册 </a>\n            </div>\n\n\n\n          </form>\n\n        </div>\n        <div class=\"text-center text-white mt-4\">\n          <small>&copy; 管理系统. All rights reserved.</small>\n        </div>\n      </div>\n    </div>\n  </div>\n  <el-dialog title=\"用户注册\" v-model=\"userRegVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n    <el-form :model=\"userFormData\" label-width=\"20%\" ref=\"userFormDataRef\" :rules=\"userRules\" align=\"left\">\n      <el-form-item label=\"用户名\" prop=\"ulname\">\n        <el-input v-model=\"userFormData.ulname\" placeholder=\"用户名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"登录密码\" prop=\"loginpassword\">\n        <el-input type=\"password\" v-model=\"userFormData.loginpassword\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码\" prop=\"loginpassword2\">\n        <el-input type=\"password\" v-model=\"userFormData.loginpassword2\" placeholder=\"确认密码\"\n          style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"uname\">\n        <el-input v-model=\"userFormData.uname\" placeholder=\"姓名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"userFormData.gender\">\n          <el-radio label=\"男\">\n            男\n          </el-radio>\n          <el-radio label=\"女\">\n            女\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"userFormData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"userReg\" :loading=\"btnLoading\">注 册</el-button>\n      </el-form-item>\n    </el-form>\n  </el-dialog>\n\n  <el-dialog title=\"工作人员注册\" v-model=\"staffRegVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n    <el-form :model=\"staffFormData\" label-width=\"20%\" ref=\"staffFormDataRef\" :rules=\"staffRules\" align=\"left\">\n      <el-form-item label=\"用户名\" prop=\"plname\">\n        <el-input v-model=\"staffFormData.plname\" placeholder=\"用户名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"登录密码\" prop=\"pword\">\n        <el-input type=\"password\" v-model=\"staffFormData.pword\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码\" prop=\"pword2\">\n        <el-input type=\"password\" v-model=\"staffFormData.pword2\" placeholder=\"确认密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"pname\">\n        <el-input v-model=\"staffFormData.pname\" placeholder=\"姓名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"staffFormData.gender\">\n          <el-radio label=\"男\">\n            男\n          </el-radio>\n          <el-radio label=\"女\">\n            女\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"年龄\" prop=\"age\">\n        <el-input v-model=\"staffFormData.age\" placeholder=\"年龄\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"staffFormData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系地址\" prop=\"address\">\n        <el-input v-model=\"staffFormData.address\" placeholder=\"联系地址\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"staffReg\" :loading=\"btnLoading\">注 册</el-button>\n      </el-form-item>\n    </el-form>\n  </el-dialog>\n</template>\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n      add: true, //是否是添加\n      formVisible: false,\n      formData: {},\n\n      addrules: {\n        ulname: [{ required: true, message: '请输入用户名', trigger: 'blur' },],\n        loginpassword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n        loginpassword2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.loginpassword) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n        uname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n        uflag: [{ required: true, message: '请输入审核状态', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n      add: true, //是否是添加\n      formVisible: false,\n      formData: {},\n\n      addrules: {\n        plname: [{ required: true, message: '请输入用户名', trigger: 'blur' },],\n        pword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n        pword2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.pword) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n        pname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n        age: [{ required: true, message: '请输入年龄', trigger: 'blur' },],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\n        status: [{ required: true, message: '请输入审核状态', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n\n    };\n  },\n  mounted() { },\n  created() {\n\n  },\n  methods: {\n    login() {\n      let that = this;\n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }\n\n      this.loading = true;\n      var role = that.loginModel.radio; //获取身份\n      if (role == '管理员') {\n        let url = base + \"/admin/login\";\n        this.loginModel2.aname = this.loginModel.username;\n        this.loginModel2.password = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.aname);\n            sessionStorage.setItem(\"role\", \"管理员\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n      else if (role == '工作人员') {\n        let url = base + \"/propertystaff/login\";\n        this.loginModel2.plname = this.loginModel.username;\n        this.loginModel2.pword = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.plname);\n            sessionStorage.setItem(\"role\", \"工作人员\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n      else if (role == '用户') {\n        let url = base + \"/users/login\";\n        this.loginModel2.ulname = this.loginModel.username;\n        this.loginModel2.loginpassword = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.ulname);\n            sessionStorage.setItem(\"role\", \"用户\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n\n\n    },\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/users/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          this.formData.uflag = \"待审核\";\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/users/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          this.formData.status = \"待审核\";\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/propertystaff/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请登录！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n\n\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import url(../assets/css/ht_bootstrap.min.css);\n@import url(../assets/css/ht_bootstrap-icons.css);\n@import url(../assets/css/ht_style.css);\n\n.role-selection {\n  display: flex;\n  justify-content: center;\n  gap: 30px;\n  padding: 10px 0;\n}\n\n.role-option {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n}\n\n.role-option input[type='radio'] {\n  width: 18px;\n  height: 18px;\n  margin: 0;\n  cursor: pointer;\n}\n\n.role-option label {\n  margin: 0;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #6e707e;\n}\n\n.role-option:hover label {\n  color: var(--primary-color);\n}\n</style>\n"]}]}