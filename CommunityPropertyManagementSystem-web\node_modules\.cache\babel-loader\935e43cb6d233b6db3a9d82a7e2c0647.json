{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\Header.vue?vue&type=template&id=61dd7a3d&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\Header.vue", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "class", "_createElementVNode", "id", "style", "src", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_dropdown", "trigger", "dropdown", "_withCtx", "_component_el_dropdown_menu", "_component_el_dropdown_item", "_component_router_link", "to", "target", "_hoisted_7", "_hoisted_9", "divided", "onClick", "$options", "exit", "_hoisted_11", "_hoisted_5", "_hoisted_6", "_toDisplayString", "$data", "role"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\Header.vue"], "sourcesContent": ["<template>\r\n  <nav class=\"navbar navbar-expand-lg navbar-light bg-white\">\r\n    <div class=\"container-fluid\">\r\n      <div class=\"ms-auto d-flex align-items-center\">\r\n        <div class=\"current-time me-4\">\r\n          <i class=\"bi bi-clock me-2\"></i>\r\n          <span id=\"currentTime\"></span>\r\n        </div>\r\n        <el-dropdown trigger=\"click\">\r\n          <div class=\"el-dropdown-link d-flex align-items-center\" style=\"cursor: pointer\">\r\n            <img\r\n                src=\"../assets/images/touxiang.png\"\r\n                class=\"rounded-circle me-2\"\r\n                style=\"width: 32px; height: 32px; object-fit: cover\"\r\n            />\r\n            <span>{{ role }}</span>\r\n          </div>\r\n          <template #dropdown>\r\n            <el-dropdown-menu>\r\n              <el-dropdown-item>\r\n                <router-link to=\"/\" class=\"dropdown-link\" target=\"_blank\">\r\n                  <i class=\"bi bi-house me-2\"></i>网站首页\r\n                </router-link>\r\n              </el-dropdown-item>\r\n              <el-dropdown-item>\r\n                <router-link to=\"/password\" class=\"dropdown-link\">\r\n                  <i class=\"bi bi-key me-2\"></i>修改密码\r\n                </router-link>\r\n              </el-dropdown-item>\r\n              <el-dropdown-item divided @click=\"exit\">\r\n                <i class=\"bi bi-box-arrow-right me-2\"></i>退出登录\r\n              </el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </template>\r\n        </el-dropdown>\r\n      </div>\r\n    </div>\r\n  </nav>\r\n</template>\r\n\r\n<script>\r\nimport { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus';\r\n\r\nexport default {\r\n  components: {\r\n    ElDropdown,\r\n    ElDropdownMenu,\r\n    ElDropdownItem,\r\n  },\r\n  data() {\r\n    return {\r\n      userLname: '',\r\n      role: '',\r\n      currentTime: '',\r\n      timer: null,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem('userLname');\r\n    this.role = sessionStorage.getItem('role');\r\n    this.updateTime();\r\n    this.timer = setInterval(this.updateTime, 1000);\r\n  },\r\n  beforeUnmount() {\r\n    if (this.timer) {\r\n      clearInterval(this.timer);\r\n    }\r\n  },\r\n  methods: {\r\n    updateTime() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      const date = String(now.getDate()).padStart(2, '0');\r\n      const hours = String(now.getHours()).padStart(2, '0');\r\n      const minutes = String(now.getMinutes()).padStart(2, '0');\r\n      const seconds = String(now.getSeconds()).padStart(2, '0');\r\n      const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];\r\n      const weekDay = weekDays[now.getDay()];\r\n\r\n      this.currentTime = `${year}年${month}月${date}日 ${hours}:${minutes}:${seconds} ${weekDay}`;\r\n      document.getElementById('currentTime').textContent = this.currentTime;\r\n    },\r\n    exit() {\r\n      this.$confirm('确认退出吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem('userLname');\r\n            sessionStorage.removeItem('role');\r\n            this.$router.push('/');\r\n          })\r\n          .catch(() => {\r\n          });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.navbar {\r\n  background: white;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1050;\r\n}\r\n\r\n.current-time {\r\n  font-size: 0.95rem;\r\n  color: #666;\r\n  min-width: 300px;\r\n  text-align: right;\r\n}\r\n\r\n.dropdown-link {\r\n  text-decoration: none;\r\n  color: inherit;\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n:deep(.el-dropdown-menu__item) {\r\n  padding: 8px 16px;\r\n  line-height: 1.5;\r\n  min-width: 120px;\r\n}\r\n\r\n:deep(.el-dropdown-menu__item i) {\r\n  margin-right: 8px;\r\n}\r\n\r\n:deep(.el-popper) {\r\n  min-width: 150px;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";OAWgBA,UAAmC;;;EAV5CC,KAAK,EAAC;AAA+C;;EACnDA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAmC;gEAC5CC,mBAAA,CAGM;EAHDD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAgC;EAA7BD,KAAK,EAAC;AAAkB,I,aAC3BC,mBAAA,CAA8B;EAAxBC,EAAE,EAAC;AAAa,G;;EAGjBF,KAAK,EAAC,4CAA4C;EAACG,KAAuB,EAAvB;IAAA;EAAA;;gEACtDF,mBAAA,CAIE;EAHEG,GAAmC,EAAnCL,UAAmC;EACnCC,KAAK,EAAC,qBAAqB;EAC3BG,KAAoD,EAApD;IAAA;IAAA;IAAA;EAAA;;gEAQEF,mBAAA,CAAgC;EAA7BD,KAAK,EAAC;AAAkB;iDAAK,OAClC;gEAIEC,mBAAA,CAA8B;EAA3BD,KAAK,EAAC;AAAgB;kDAAK,OAChC;iEAGAC,mBAAA,CAA0C;EAAvCD,KAAK,EAAC;AAA4B;kDAAK,OAC5C;;;;;;uBA9BZK,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJL,mBAAA,CAkCM,OAlCNM,UAkCM,GAjCJN,mBAAA,CAgCM,OAhCNO,UAgCM,GA/BJC,UAGM,EACNC,YAAA,CA0BcC,sBAAA;IA1BDC,OAAO,EAAC;EAAO;IASfC,QAAQ,EAAAC,QAAA,CACjB,MAcmB,CAdnBJ,YAAA,CAcmBK,2BAAA;wBAbjB,MAImB,CAJnBL,YAAA,CAImBM,2BAAA;0BAHjB,MAEc,CAFdN,YAAA,CAEcO,sBAAA;UAFDC,EAAE,EAAC,GAAG;UAAClB,KAAK,EAAC,eAAe;UAACmB,MAAM,EAAC;;4BAC/C,MAAgC,CAAhCC,UAAgC,E;;;;;UAGpCV,YAAA,CAImBM,2BAAA;0BAHjB,MAEc,CAFdN,YAAA,CAEcO,sBAAA;UAFDC,EAAE,EAAC,WAAW;UAAClB,KAAK,EAAC;;4BAChC,MAA8B,CAA9BqB,UAA8B,E;;;;;UAGlCX,YAAA,CAEmBM,2BAAA;QAFDM,OAAO,EAAP,EAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC;;0BAChC,MAA0C,CAA1CC,WAA0C,E;;;;;;sBArBhD,MAOM,CAPNzB,mBAAA,CAOM,OAPN0B,UAOM,GANJC,UAIE,EACF3B,mBAAA,CAAuB,cAAA4B,gBAAA,CAAdC,KAAA,CAAAC,IAAI,iB"}]}