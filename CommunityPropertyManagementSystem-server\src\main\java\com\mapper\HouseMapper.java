package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.House;

public interface HouseMapper {

	//返回所有记录
	public List<House> findHouseList();
	
	//查询多条记录
	public List<House> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertHouse(House house);

	//根据ID删除
	public int deleteHouse(int id);
	
	//更新
	public int updateHouse(House house);
	
	//根据ID得到对应的记录
	public House queryHouseById(int id);
	
}

