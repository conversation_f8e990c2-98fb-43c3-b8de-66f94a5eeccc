{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseManage.vue", "mtime": 1749135723854}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCByZXF1ZXN0LCB7IGJhc2UgfSBmcm9tICIuLi8uLi8uLi8uLi91dGlscy9odHRwIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdob3VzZScsCiAgY29tcG9uZW50czoge30sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZpbHRlcnM6IHsKICAgICAgICAvL+WIl+ihqOafpeivouWPguaVsAogICAgICAgIGhubzogJycsCiAgICAgICAgYnVpbGRpbmZvOiAnJwogICAgICB9LAogICAgICBwYWdlOiB7CiAgICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgICAgLy8g5b2T5YmN6aG1CiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIC8vIOavj+mhteaYvuekuuadoeebruS4quaVsAogICAgICAgIHRvdGFsQ291bnQ6IDAgLy8g5oC75p2h55uu5pWwCiAgICAgIH0sCgogICAgICBpc0NsZWFyOiBmYWxzZSwKICAgICAgbGlzdExvYWRpbmc6IGZhbHNlLAogICAgICAvL+WIl+ihqOWKoOi9veeKtuaAgQogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwKICAgICAgLy/kv53lrZjmjInpkq7liqDovb3nirbmgIEKICAgICAgZGF0YWxpc3Q6IFtdIC8v6KGo5qC85pWw5o2uICAKICAgIH07CiAgfSwKCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0RGF0YXMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWIoOmZpOaIv+Wxi+S/oeaBrwogICAgaGFuZGxlRGVsZXRlKGluZGV4LCByb3cpIHsKICAgICAgdGhpcy4kY29uZmlybSgi56Gu6K6k5Yig6Zmk6K+l6K6w5b2V5ZCXPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgICBsZXQgdXJsID0gYmFzZSArICIvaG91c2UvZGVsP2lkPSIgKyByb3cuaGlkOwogICAgICAgIHJlcXVlc3QucG9zdCh1cmwpLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfIiwKICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICBvZmZzZXQ6IDMyMAogICAgICAgICAgfSk7CiAgICAgICAgICB0aGlzLmdldERhdGFzKCk7CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvLyDliIbpobUKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucGFnZS5jdXJyZW50UGFnZSA9IHZhbDsKICAgICAgdGhpcy5nZXREYXRhcygpOwogICAgfSwKICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICBnZXREYXRhcygpIHsKICAgICAgdmFyIHVzZXIgPSBKU09OLnBhcnNlKHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3VzZXInKSk7IC8v6I635Y+W55So5oi35L+h5oGvCgogICAgICBsZXQgcGFyYSA9IHsKICAgICAgICB1aWQ6IHVzZXIudWlkCiAgICAgIH07CiAgICAgIHRoaXMubGlzdExvYWRpbmcgPSB0cnVlOwogICAgICBsZXQgdXJsID0gYmFzZSArICIvaG91c2UvbGlzdD9jdXJyZW50UGFnZT0iICsgdGhpcy5wYWdlLmN1cnJlbnRQYWdlICsgIiZwYWdlU2l6ZT0iICsgdGhpcy5wYWdlLnBhZ2VTaXplOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5yZXNkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMuaXNQYWdlID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5pc1BhZ2UgPSBmYWxzZTsKICAgICAgICB9CiAgICAgICAgdGhpcy5wYWdlLnRvdGFsQ291bnQgPSByZXMuY291bnQ7CiAgICAgICAgdGhpcy5kYXRhbGlzdCA9IHJlcy5yZXNkYXRhOwogICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy/mn6Xor6IKICAgIHF1ZXJ5KCkgewogICAgICB0aGlzLmdldERhdGFzKCk7CiAgICB9LAogICAgLy8g5p+l55yLCiAgICBoYW5kbGVTaG93KGluZGV4LCByb3cpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICIvSG91c2VEZXRhaWwiLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBpZDogcm93LmhpZAogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g57yW6L6RCiAgICBoYW5kbGVFZGl0KGluZGV4LCByb3cpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICIvSG91c2VFZGl0IiwKICAgICAgICBxdWVyeTogewogICAgICAgICAgaWQ6IHJvdy5oaWQKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "hno", "buildinfo", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "created", "getDatas", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "hid", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "user", "JSON", "parse", "sessionStorage", "getItem", "para", "uid", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "id", "handleEdit"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseManage.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"hno\" label=\"门牌号\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"buildinfo\" label=\"所属楼栋\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"htype\" label=\"户型\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"areasinfo\" label=\"面积/m2\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"hord\" label=\"朝向\"  align=\"center\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\" style=\" padding: 3px 6px 3px 6px;\">编辑</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" \n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'house',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          hno: '',\n          buildinfo: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        \n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据  \n    \n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n \n    methods: {    \n\n              \n       // 删除房屋信息\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/house/del?id=\" + row.hid;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n      getDatas() {      \n          var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\n         \n          let para = {\n               uid:user.uid,\n\n          };\n          this.listLoading = true;\n          let url = base + \"/house/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \n          request.post(url, para).then((res) => {   \n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },    \n                 //查询\n        query() {\n          this.getDatas();\n        },  \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/HouseDetail\",\n             query: {\n                id: row.hid,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/HouseEdit\",\n             query: {\n                id: row.hid,\n              },\n          });\n        },\n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";AAyBA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACEC,OAAO,EAAE;QACd;QACAC,GAAG,EAAE,EAAE;QACPC,SAAS,EAAE;MACb,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAEhB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACb,WAAU,GAAI,IAAI;QACvB,IAAIc,GAAE,GAAI1B,IAAG,GAAI,gBAAe,GAAIoB,GAAG,CAACO,GAAG;QAC3C5B,OAAO,CAAC6B,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACjB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACkB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAiB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAC5B,IAAI,CAACC,WAAU,GAAI2B,GAAG;MAC3B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACFA,QAAQA,CAAA,EAAG;MACP,IAAIoB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;;MAEvD,IAAIC,IAAG,GAAI;QACNC,GAAG,EAACN,IAAI,CAACM;MAEd,CAAC;MACD,IAAI,CAAC9B,WAAU,GAAI,IAAI;MACvB,IAAIc,GAAE,GAAI1B,IAAG,GAAI,0BAAyB,GAAI,IAAI,CAACO,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACtGV,OAAO,CAAC6B,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACc,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACtC,IAAI,CAACG,UAAS,GAAImB,GAAG,CAACiB,KAAK;QAChC,IAAI,CAAChC,QAAO,GAAIe,GAAG,CAACc,OAAO;QAC3B,IAAI,CAAC/B,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACQ;IACTmC,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC/B,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAgC,UAAUA,CAAC7B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC6B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,cAAc;QACnBJ,KAAK,EAAE;UACJK,EAAE,EAAEhC,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACA0B,UAAUA,CAAClC,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC6B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,YAAY;QACjBJ,KAAK,EAAE;UACJK,EAAE,EAAEhC,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ;EACF;AACN"}]}