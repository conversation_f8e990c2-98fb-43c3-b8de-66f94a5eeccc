{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseManage.vue", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "hno", "buildinfo", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "created", "getDatas", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "hid", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "para", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "id", "handleEdit"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseManage.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\r\n<el-form :inline=\"true\" :model=\"filters\" >\r\n<el-form-item>\r\n<el-input v-model=\"filters.hno\" placeholder=\"门牌号\"  size=\"small\"></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-input v-model=\"filters.buildinfo\" placeholder=\"所属楼栋\"  size=\"small\"></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\r\n</el-form-item>\r\n </el-form>\r\n</el-col>\r\n\r\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\r\n<el-table-column prop=\"hno\" label=\"门牌号\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"buildinfo\" label=\"所属楼栋\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"htype\" label=\"户型\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"areasinfo\" label=\"面积/m2\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"hord\" label=\"朝向\"  align=\"center\"></el-table-column>\r\n<el-table-column prop=\"uid\" label=\"用户id\"  align=\"center\"></el-table-column>\r\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\r\n<template #default=\"scope\">\r\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\r\n<el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\" style=\" padding: 3px 6px 3px 6px;\">编辑</el-button>\r\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\r\n</template>\r\n</el-table-column>\r\n</el-table>\r\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \r\n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" \r\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'house',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\r\n          //列表查询参数\r\n          hno: '',\r\n          buildinfo: '',\r\n        },\r\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        \n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据  \n    \n      };\n    },\n    created() {\r\n      this.getDatas();\r\n    },\r\n\r\n \n    methods: {    \n\n              \n       // 删除房屋信息\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/house/del?id=\" + row.hid;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n        getDatas() {      \n          let para = {\n               hno:this.filters.hno,\r\n   buildinfo:this.filters.buildinfo,\r\n\n          };\n          this.listLoading = true;\n          let url = base + \"/house/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \n          request.post(url, para).then((res) => {   \n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },    \n                 //查询\n        query() {\n          this.getDatas();\n        },  \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/HouseDetail\",\n             query: {\n                id: row.hid,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/HouseEdit\",\n             query: {\n                id: row.hid,\n              },\n          });\n        },\n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";AAuCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACEC,OAAO,EAAE;QACd;QACAC,GAAG,EAAE,EAAE;QACPC,SAAS,EAAE;MACb,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAEhB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACb,WAAU,GAAI,IAAI;QACvB,IAAIc,GAAE,GAAI1B,IAAG,GAAI,gBAAe,GAAIoB,GAAG,CAACO,GAAG;QAC3C5B,OAAO,CAAC6B,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACjB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACkB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAiB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAC5B,IAAI,CAACC,WAAU,GAAI2B,GAAG;MAC3B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIoB,IAAG,GAAI;QACN/B,GAAG,EAAC,IAAI,CAACD,OAAO,CAACC,GAAG;QAChCC,SAAS,EAAC,IAAI,CAACF,OAAO,CAACE;MAEhB,CAAC;MACD,IAAI,CAACM,WAAU,GAAI,IAAI;MACvB,IAAIc,GAAE,GAAI1B,IAAG,GAAI,0BAAyB,GAAI,IAAI,CAACO,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACtGV,OAAO,CAAC6B,IAAI,CAACF,GAAG,EAAEU,IAAI,CAAC,CAACX,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACQ,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAAChC,IAAI,CAACG,UAAS,GAAImB,GAAG,CAACW,KAAK;QAChC,IAAI,CAAC1B,QAAO,GAAIe,GAAG,CAACQ,OAAO;QAC3B,IAAI,CAACzB,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACQ;IACT6B,KAAKA,CAAA,EAAG;MACN,IAAI,CAACzB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACA0B,UAAUA,CAACvB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACuB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,cAAc;QACnBJ,KAAK,EAAE;UACJK,EAAE,EAAE1B,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAoB,UAAUA,CAAC5B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACuB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,YAAY;QACjBJ,KAAK,EAAE;UACJK,EAAE,EAAE1B,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ;EACF;AACN"}]}