package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/house")
public class HouseController{
	
	@Resource
	private HouseService houseService;
	
	//房屋信息列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<House>> list(@RequestBody House house, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = houseService.getCount(house);
		//获取当前页记录
		List<House> houseList = houseService.queryHouseList(house, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(houseList, counts, page_count);
	}
        
	//添加房屋信息
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody House house, HttpServletRequest req) throws Exception {
		try {
			//判断用户名是否存在
            House house1 = new House();
            house1.setHno(house.getHno());
            List<House> houseList = houseService.queryHouseList(house1, null);
            if (houseList.size() > 0) {
                return Response.error(201, "门牌号已存在，请重新输入");
            }
            else
            {
                houseService.insertHouse(house); //添加
            }
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除房屋信息
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			houseService.deleteHouse(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改房屋信息
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody House house, HttpServletRequest req) throws Exception {
		try {
			houseService.updateHouse(house); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回房屋信息详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			House house=houseService.queryHouseById(id); //根据ID查询
			return Response.success(house);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

