<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.HouseMapper">
	<select id="findHouseList"  resultType="House">
		select * from house 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="House">
	    select  *  
        from house a  	
		<where>
      		<if test="hid != null and hid !=0 ">
		    and a.hid = #{hid}
		</if>
		<if test="hno != null and hno != ''">
		    and a.hno = #{hno}
		</if>
		<if test="buildinfo != null and buildinfo != ''">
		    and a.buildinfo = #{buildinfo}
		</if>
		<if test="htype != null and htype != ''">
		    and a.htype = #{htype}
		</if>
		<if test="hord != null and hord != ''">
		    and a.hord = #{hord}
		</if>
		<if test="uid != null and uid !=0 ">
		    and a.uid = #{uid}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} hid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from house a  
		<where>
      		<if test="hid != null and hid !=0 ">
		    and a.hid = #{hid}
		</if>
		<if test="hno != null and hno != ''">
		    and a.hno = #{hno}
		</if>
		<if test="buildinfo != null and buildinfo != ''">
		    and a.buildinfo = #{buildinfo}
		</if>
		<if test="htype != null and htype != ''">
		    and a.htype = #{htype}
		</if>
		<if test="hord != null and hord != ''">
		    and a.hord = #{hord}
		</if>
		<if test="uid != null and uid !=0 ">
		    and a.uid = #{uid}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryHouseById" parameterType="int" resultType="House">
    select  *  
     from house a  	 where a.hid=#{value}
  </select>
 
	<insert id="insertHouse" useGeneratedKeys="true" keyProperty="hid" parameterType="House">
    insert into house
    (hno,buildinfo,htype,areasinfo,hord,uid)
    values
    (#{hno},#{buildinfo},#{htype},#{areasinfo},#{hord},#{uid});
  </insert>
	
	<update id="updateHouse" parameterType="House" >
    update house 
    <set>
		<if test="hno != null and hno != ''">
		    hno = #{hno},
		</if>
		<if test="buildinfo != null and buildinfo != ''">
		    buildinfo = #{buildinfo},
		</if>
		<if test="htype != null and htype != ''">
		    htype = #{htype},
		</if>
		<if test="areasinfo != null ">
		    areasinfo = #{areasinfo},
		</if>
		<if test="hord != null and hord != ''">
		    hord = #{hord},
		</if>
		<if test="uid != null ">
		    uid = #{uid},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="hid != null or hid != ''">
      hid=#{hid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteHouse" parameterType="int">
    delete from  house where hid=#{value}
  </delete>

	
	
</mapper>

 
