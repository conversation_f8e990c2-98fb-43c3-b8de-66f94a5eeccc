package com.service;
import java.util.List;

import com.model.Announcements;
import com.util.PageBean;

public interface AnnouncementsService{
	
	//查询多条记录
	public List<Announcements> queryAnnouncementsList(Announcements announcements,PageBean page) throws Exception;
 
	//添加
	public int insertAnnouncements(Announcements announcements) throws Exception ;
	
	//根据ID删除
	public int deleteAnnouncements(int id) throws Exception ;
	
	//更新
	public int updateAnnouncements(Announcements announcements) throws Exception ;
	
	//根据ID查询单条数据
	public Announcements queryAnnouncementsById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Announcements announcements);

}

