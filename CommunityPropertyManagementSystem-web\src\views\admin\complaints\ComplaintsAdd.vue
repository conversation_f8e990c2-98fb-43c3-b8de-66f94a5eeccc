﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="类别" prop="ctype">
<el-select v-model="formData.ctype" placeholder="请选择"  size="small">
<el-option label="投诉" value="投诉"></el-option>
<el-option label="建议" value="建议"></el-option>
</el-select>
</el-form-item>
<el-form-item label="标题" prop="ctitle">
<el-input v-model="formData.ctitle" placeholder="标题"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="具体描述" prop="ccontent">
<WangEditor  ref="wangEditorRef" v-model="formData.ccontent" :config="editorConfig"   :isClear="isClear" @change="editorChange"></WangEditor>
</el-form-item>

<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
import WangEditor from "../../../components/WangEditor";
export default {
  name: 'ComplaintsAdd',
  components: {
    WangEditor,
  },  
    data() {
      return {   
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          ctype: [{ required: true, message: '请选择类别', trigger: 'onchange' }],
          ctitle: [{ required: true, message: '请输入标题', trigger: 'blur' },
],          uid: [{ required: true, message: '请输入用户id', trigger: 'blur' },
],          cstatus: [{ required: true, message: '请输入处理状态', trigger: 'blur' },
],          cresult: [{ required: true, message: '请输入处理结果', trigger: 'blur' },
],        },

      };
    },
    mounted() {
    
    },

 
    methods: {    
   // 添加
    save() {       
         this.$refs["formDataRef"].validate((valid) => { //验证表单
           if (valid) {
             let url = base + "/complaints/add";
             this.btnLoading = true;
             var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息
             this.formData.uid = user.uid;
             this.formData.cstatus = "未处理";
             request.post(url, this.formData).then((res) => { //发送请求         
               if (res.code == 200) {
                 this.$message({
                   message: "操作成功",
                   type: "success",
                   offset: 320,
                 });              
                this.$router.push({
                path: "/ComplaintsManage2",
                });
               } else {
                 this.$message({
                   message: res.msg,
                   type: "error",
                   offset: 320,
                 });
               }
               this.btnLoading=false;
             });
           }        
           
         });
    },
    
       // 返回
        goBack() {
          this.$router.push({
            path: "/ComplaintsManage2",
          });
        },       
              
          
           
            // 富文本编辑器
    editorChange(val) {
      this.formData.ccontent = val;
    },
   
      },
}

</script>
<style scoped>
</style>
 

