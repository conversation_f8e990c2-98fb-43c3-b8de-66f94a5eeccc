package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
public class StatisticsController {

    @Resource
    private UsersService usersService;
    
    @Resource
    private PropertystaffService propertystaffService;
    
    @Resource
    private AnnouncementsService announcementsService;
    
    @Resource
    private ComplaintsService complaintsService;
    
    @Resource
    private ExpenseinfoService expenseinfoService;
    
    @Resource
    private HouseService houseService;

    // 获取管理员统计数据
    @ResponseBody
    @PostMapping(value = "/admin")
    @CrossOrigin
    public Response getAdminStatistics(HttpServletRequest req) throws Exception {
        try {
            Map<String, Integer> statistics = new HashMap<>();
            
            // 获取用户总数
            Users users = new Users();
            int userCount = usersService.getCount(users);
            statistics.put("userCount", userCount);
            
            // 获取工作人员总数
            Propertystaff propertystaff = new Propertystaff();
            int staffCount = propertystaffService.getCount(propertystaff);
            statistics.put("staffCount", staffCount);
            
            return Response.success(statistics);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error();
        }
    }

    // 获取工作人员统计数据
    @ResponseBody
    @PostMapping(value = "/staff")
    @CrossOrigin
    public Response getStaffStatistics(HttpServletRequest req) throws Exception {
        try {
            Map<String, Integer> statistics = new HashMap<>();
            
            // 获取用户总数
            Users users = new Users();
            int userCount = usersService.getCount(users);
            statistics.put("userCount", userCount);
            
            // 获取公告信息总数
            Announcements announcements = new Announcements();
            int announcementCount = announcementsService.getCount(announcements);
            statistics.put("announcementCount", announcementCount);
            
            // 获取投诉建议总数
            Complaints complaints = new Complaints();
            int complaintCount = complaintsService.getCount(complaints);
            statistics.put("complaintCount", complaintCount);
            
            // 获取费用信息总数
            Expenseinfo expenseinfo = new Expenseinfo();
            int expenseCount = expenseinfoService.getCount(expenseinfo);
            statistics.put("expenseCount", expenseCount);
            
            return Response.success(statistics);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error();
        }
    }

    // 获取用户统计数据
    @ResponseBody
    @PostMapping(value = "/usertotal")
    @CrossOrigin
    public Response getUserStatistics(@RequestBody Map<String, Object> params, HttpServletRequest req) throws Exception {
        try {
            Map<String, Integer> statistics = new HashMap<>();
            Integer userId = (Integer) params.get("userId");

            if (userId == null) {
                return Response.error(201, "用户ID不能为空");
            }

            // 获取用户的房屋信息数量
            House house = new House();
            house.setUid(userId);
            int houseCount = houseService.getCount(house);
            statistics.put("houseCount", houseCount);

            // 获取用户的投诉建议数量
            Complaints complaints = new Complaints();
            complaints.setUid(userId);
            int complaintCount = complaintsService.getCount(complaints);
            statistics.put("complaintCount", complaintCount);

            // 获取用户的费用信息数量（通过房屋门牌号关联）
            int expenseCount = 0;
            try {
                // 获取用户的所有房屋
                java.util.List<House> houseList = houseService.queryHouseList(house, null);

                // 遍历每个房屋，统计费用信息
                for (House userHouse : houseList) {
                    if (userHouse.getHno() != null) {
                        Expenseinfo expenseinfo = new Expenseinfo();
                        expenseinfo.setHno(userHouse.getHno());
                        expenseCount += expenseinfoService.getCount(expenseinfo);
                    }
                }
            } catch (Exception e) {
                // 如果查询失败，返回0
                expenseCount = 0;
            }
            statistics.put("expenseCount", expenseCount);

            return Response.success(statistics);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error();
        }
    }
}
