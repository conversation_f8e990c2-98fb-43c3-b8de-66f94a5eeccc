package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Propertystaff;

public interface PropertystaffMapper {

	//返回所有记录
	public List<Propertystaff> findPropertystaffList();
	
	//查询多条记录
	public List<Propertystaff> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertPropertystaff(Propertystaff propertystaff);

	//根据ID删除
	public int deletePropertystaff(int id);
	
	//更新
	public int updatePropertystaff(Propertystaff propertystaff);
	
	//根据ID得到对应的记录
	public Propertystaff queryPropertystaffById(int id);
	
}

