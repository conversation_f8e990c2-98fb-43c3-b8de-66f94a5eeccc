<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.AnnouncementsMapper">
	<select id="findAnnouncementsList"  resultType="Announcements">
		select * from announcements 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Announcements">
	    select  *  
        from announcements a  	
		<where>
      		<if test="aid != null and aid !=0 ">
		    and a.aid = #{aid}
		</if>
		<if test="atitle != null and atitle != ''">
		    and a.atitle = #{atitle}
		</if>
		<if test="apubtime != null and apubtime != ''">
		    and a.apubtime = #{apubtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} aid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from announcements a  
		<where>
      		<if test="aid != null and aid !=0 ">
		    and a.aid = #{aid}
		</if>
		<if test="atitle != null and atitle != ''">
		    and a.atitle = #{atitle}
		</if>
		<if test="apubtime != null and apubtime != ''">
		    and a.apubtime = #{apubtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryAnnouncementsById" parameterType="int" resultType="Announcements">
    select  *  
     from announcements a  	 where a.aid=#{value}
  </select>
 
	<insert id="insertAnnouncements" useGeneratedKeys="true" keyProperty="aid" parameterType="Announcements">
    insert into announcements
    (atitle,adetail,apubtime)
    values
    (#{atitle},#{adetail},now());
  </insert>
	
	<update id="updateAnnouncements" parameterType="Announcements" >
    update announcements 
    <set>
		<if test="atitle != null and atitle != ''">
		    atitle = #{atitle},
		</if>
		<if test="adetail != null and adetail != ''">
		    adetail = #{adetail},
		</if>
		<if test="apubtime != null and apubtime != ''">
		    apubtime = #{apubtime},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="aid != null or aid != ''">
      aid=#{aid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteAnnouncements" parameterType="int">
    delete from  announcements where aid=#{value}
  </delete>

	
	
</mapper>

 
