package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.PropertystaffMapper;
import com.model.Propertystaff;
import com.util.PageBean;
@Service
public class PropertystaffServiceImpl implements PropertystaffService{
        
	@Autowired
	private PropertystaffMapper propertystaffMapper;

	//查询多条记录
	public List<Propertystaff> queryPropertystaffList(Propertystaff propertystaff,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(propertystaff, page);
		
		List<Propertystaff> getPropertystaff = propertystaffMapper.query(map);
		
		return getPropertystaff;
	}
	
	//得到记录总数
	@Override
	public int getCount(Propertystaff propertystaff) {
		Map<String, Object> map = getQueryMap(propertystaff, null);
		int count = propertystaffMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Propertystaff propertystaff,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(propertystaff!=null){
			map.put("pid", propertystaff.getPid());
			map.put("plname", propertystaff.getPlname());
			map.put("pword", propertystaff.getPword());
			map.put("pname", propertystaff.getPname());
			map.put("gender", propertystaff.getGender());
			map.put("age", propertystaff.getAge());
			map.put("contact", propertystaff.getContact());
			map.put("address", propertystaff.getAddress());
			map.put("regtime", propertystaff.getRegtime());
			map.put("status", propertystaff.getStatus());
			map.put("sort", propertystaff.getSort());
			map.put("condition", propertystaff.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertPropertystaff(Propertystaff propertystaff) throws Exception {
		return propertystaffMapper.insertPropertystaff(propertystaff);
	}

	//根据ID删除
	public int deletePropertystaff(int id) throws Exception {
		return propertystaffMapper.deletePropertystaff(id);
	}

	//更新
	public int updatePropertystaff(Propertystaff propertystaff) throws Exception {
		return propertystaffMapper.updatePropertystaff(propertystaff);
	}
	
	//根据ID得到对应的记录
	public Propertystaff queryPropertystaffById(int id) throws Exception {
		Propertystaff po =  propertystaffMapper.queryPropertystaffById(id);
		return po;
	}
}

