package com.model;
import java.util.List;

/**
* (house)房屋信息实体类
*/
public class House extends ComData{
	
	private static final long serialVersionUID = 222758451878775L;
	private Integer hid;    //房屋id
	private String hno;    //门牌号
	private String buildinfo;    //所属楼栋
	private String htype;    //户型
	private Object areasinfo;    //面积/m2
	private String hord;    //朝向
	private Integer uid;    //用户id

	public Integer getHid() {
		return hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public String getHno() {
		return hno;
	}

	public void setHno(String hno) {
		this.hno = hno;
	}

	public String getBuildinfo() {
		return buildinfo;
	}

	public void setBuildinfo(String buildinfo) {
		this.buildinfo = buildinfo;
	}

	public String getHtype() {
		return htype;
	}

	public void setHtype(String htype) {
		this.htype = htype;
	}

	public Object getAreasinfo() {
		return areasinfo;
	}

	public void setAreasinfo(Object areasinfo) {
		this.areasinfo = areasinfo;
	}

	public String getHord() {
		return hord;
	}

	public void setHord(String hord) {
		this.hord = hord;
	}

	public Integer getUid() {
		return uid;
	}

	public void setUid(Integer uid) {
		this.uid = uid;
	}

}

