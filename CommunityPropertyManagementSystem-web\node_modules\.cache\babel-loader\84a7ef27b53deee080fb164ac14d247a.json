{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749135939990}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["id", "class", "_createElementVNode", "_hoisted_90", "_hoisted_91", "_createCommentVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "href", "onClick", "_cache", "_withModifiers", "$event", "$options", "toggleSubmenu", "_hoisted_4", "_normalizeClass", "$data", "openSubmenus", "includes", "show", "_createVNode", "_component_router_link", "to", "_hoisted_5", "_hoisted_7", "_hoisted_9", "_hoisted_11", "_hoisted_12", "_hoisted_14", "_hoisted_16", "_hoisted_18", "_hoisted_19", "role", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_25", "_hoisted_27", "_hoisted_29", "_hoisted_31", "_hoisted_32", "_hoisted_34", "_hoisted_36", "_hoisted_38", "_hoisted_39", "_hoisted_41", "_hoisted_43", "_hoisted_45", "_hoisted_46", "_hoisted_48", "_hoisted_50", "_hoisted_51", "_hoisted_53", "_hoisted_55", "_hoisted_57", "_hoisted_58", "_hoisted_60", "_hoisted_62", "_hoisted_64", "_hoisted_65", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_71", "_hoisted_72", "_hoisted_74", "_hoisted_76", "_hoisted_77", "_hoisted_79", "_hoisted_81", "_hoisted_82", "_hoisted_84", "_hoisted_85", "_hoisted_87", "_hoisted_89", "args", "exit"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n  <!-- 侧边栏 -->\r\n  <nav id=\"sidebar\">\r\n    <div class=\"sidebar-header\">\r\n      <div class=\"d-flex align-items-center p-3\">\r\n        <i class=\"bi bi-shield-check text-white me-2\" style=\"font-size: 1.5rem\"></i>\r\n        <h5 class=\"mb-0\"><a href=\"/main\" style=\"color: #fff;text-decoration: none;\">小区物业管理系统</a></h5>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '管理员'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n                <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>审核用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n    \r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n                    <li><router-link to=\"/propertystaffManage2\"> <i class=\"bi bi-circle me-2\"></i>审核工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n   \r\n\r\n        </ul>\r\n      </li>\r\n\r\n\r\n\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '工作人员'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n          <li><router-link to=\"/usersInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu3\" @click.prevent=\"toggleSubmenu('userSubmenu3')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>公告管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu3') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu3') }\" id=\"userSubmenu3\">\r\n          <li><router-link to=\"/announcementsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage\"> <i class=\"bi bi-circle me-2\"></i>管理公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage2\"> <i class=\"bi bi-circle me-2\"></i>公告列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu4\" @click.prevent=\"toggleSubmenu('userSubmenu4')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>投诉建议管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu4') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu4') }\" id=\"userSubmenu4\">\r\n          <li><router-link to=\"/complaintsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage\"> <i class=\"bi bi-circle me-2\"></i>管理投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage2\"> <i class=\"bi bi-circle me-2\"></i>投诉建议列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu5\" @click.prevent=\"toggleSubmenu('userSubmenu5')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>房屋信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu5') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu5') }\" id=\"userSubmenu5\">\r\n          <li><router-link to=\"/houseAdd\"> <i class=\"bi bi-circle me-2\"></i>添加房屋信息</router-link></li>\r\n          <li><router-link to=\"/houseManage\"> <i class=\"bi bi-circle me-2\"></i>管理房屋信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu7\" @click.prevent=\"toggleSubmenu('userSubmenu7')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>费用信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu7') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu7') }\" id=\"userSubmenu7\">\r\n          <li><router-link to=\"/expenseinfoAdd\"> <i class=\"bi bi-circle me-2\"></i>添加费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage\"> <i class=\"bi bi-circle me-2\"></i>管理费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage2\"> <i class=\"bi bi-circle me-2\"></i>费用信息列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '用户'\">\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu3\" @click.prevent=\"toggleSubmenu('userSubmenu3')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>公告管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu3') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu3') }\" id=\"userSubmenu3\">\r\n  \r\n          <li><router-link to=\"/announcementsManage2\"> <i class=\"bi bi-circle me-2\"></i>公告列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n            <li>\r\n        <a href=\"#userSubmenu5\" @click.prevent=\"toggleSubmenu('userSubmenu5')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>房屋信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu5') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu5') }\" id=\"userSubmenu5\">\r\n          <li><router-link to=\"/houseAdd\"> <i class=\"bi bi-circle me-2\"></i>添加房屋信息</router-link></li>\r\n          <li><router-link to=\"/houseManage\"> <i class=\"bi bi-circle me-2\"></i>管理房屋信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu4\" @click.prevent=\"toggleSubmenu('userSubmenu4')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>投诉建议管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu4') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu4') }\" id=\"userSubmenu4\">\r\n          <li><router-link to=\"/complaintsAdd\"> <i class=\"bi bi-circle me-2\"></i>提交投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage2\"> <i class=\"bi bi-circle me-2\"></i>我的投诉建议</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu7\" @click.prevent=\"toggleSubmenu('userSubmenu7')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>费用信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu7') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu7') }\" id=\"userSubmenu7\">\r\n    \r\n          <li><router-link to=\"/expenseinfoManage2\"> <i class=\"bi bi-circle me-2\"></i>我的费用信息列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>个人中心</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n                 <li><router-link to=\"/usersInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n    <div class=\"sidebar-footer\">\r\n      <a href=\"javascript:void(0);\" @click=\"exit\" class=\"d-flex align-items-center text-white text-decoration-none p-3\">\r\n        <i class=\"bi bi-box-arrow-left me-2\"></i>\r\n        <span>退出登录</span>\r\n      </a>\r\n    </div>\r\n  </nav>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'LeftMenu',\r\n  data() {\r\n    return {\r\n      userLname: '',\r\n      role: '',\r\n      activeMenuItem: 'dashboard',\r\n      openSubmenus: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem('userLname');\r\n    this.role = sessionStorage.getItem('role');\r\n  },\r\n  methods: {\r\n    setActiveMenuItem(menuItem) {\r\n      this.activeMenuItem = menuItem;\r\n    },\r\n    toggleSubmenu(submenuId) {\r\n      const index = this.openSubmenus.indexOf(submenuId);\r\n      if (index === -1) {\r\n        // Close other submenus before opening new one\r\n        this.openSubmenus = [submenuId];\r\n      } else {\r\n        this.openSubmenus.splice(index, 1);\r\n      }\r\n    },\r\n    exit() {\r\n      this.$confirm('确认退出吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem('userLname');\r\n          sessionStorage.removeItem('role');\r\n          this.$router.push('/');\r\n        })\r\n        .catch(() => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.bi-chevron-down {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.rotate-180 {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.collapse {\r\n  transition: all 0.3s ease-out;\r\n}\r\n\r\n.collapse.show {\r\n  display: block;\r\n}\r\n\r\n.sub-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sub-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sub-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.left-menu {\r\n  width: 250px;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100vh;\r\n  z-index: 999;\r\n  background: #fff;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n#content {\r\n  width: calc(100% - 250px);\r\n  margin-left: 250px;\r\n  transition: all 0.3s;\r\n  min-height: 100vh;\r\n}\r\n\r\n:global(.sidebar-collapsed) #content {\r\n  margin-left: 0;\r\n  width: 100%;\r\n}\r\n\r\n:global(.sidebar-collapsed) .left-menu {\r\n  margin-left: -250px;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;EAEOA,EAAE,EAAC;AAAS;;;EAUXC,KAAK,EAAC;AAA0B;gEAI9BC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAiB,cAAX,MAAI,E;gEAKqBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;iDAAK,MAAI;gEAC3BC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;iDAAK,MAAI;gEAC5CC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEASzEC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAmB,cAAb,QAAM,E;iEAK2BA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACzBC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAClDC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAYnFC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAiB,cAAX,MAAI,E;iEAMqBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;;EAQxEA,KAAK,EAAC;AAA0B;iEAI9BC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAiB,cAAX,MAAI,E;iEAKqBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAClCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACpCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACxCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAOzEC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAiB,cAAX,MAAI,E;iEAK6BA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAClCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACpCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAOlFC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAmB,cAAb,QAAM,E;iEAKwBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACtCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAOjFC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAmB,cAAb,QAAM,E;iEAKmBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAO3EC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAmB,cAAb,QAAM,E;iEAK2BA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACzCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAOjFC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAmB,cAAb,QAAM,E;iEAKyBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACtCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAQlFC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAiB,cAAX,MAAI,E;iEAMqBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;;EASxEA,KAAK,EAAC;AAA0B;iEAK9BC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAiB,cAAX,MAAI,E;iEAMiCA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAQlFC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAmB,cAAb,QAAM,E;iEAKmBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAQ3EC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAmB,cAAb,QAAM,E;iEAKwBA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACnCC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEASjFC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAmB,cAAb,QAAM,E;iEAM6BA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,UAAQ;iEAQpFC,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,aAC5BC,mBAAA,CAAiB,cAAX,MAAI,E;iEAM6BA,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAC/CC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB;kDAAK,MAAI;;EAQvEA,KAAK,EAAC;AAAgB;iEAEvBC,mBAAA,CAAyC;EAAtCD,KAAK,EAAC;AAA2B;iEACpCC,mBAAA,CAAiB,cAAX,MAAI;qBADVC,WAAyC,EACzCC,WAAiB,C;;;6DAtRvBC,mBAAA,SAAY,EACZH,mBAAA,CAwRM,OAxRNI,UAwRM,GAvRJC,UAKM,E,gBAINL,mBAAA,CAwDK,MAxDLM,UAwDK,GAvDHN,mBAAA,CAgBK,aAfHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNe,UAGM,EACNd,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAMK;IANDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAAyF,aAArFoB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCC,UAAiC,E;;QAC5DvB,mBAAA,CAA6F,aAAzFoB,YAAA,CAAoFC,sBAAA;IAAvEC,EAAE,EAAC;EAAe;sBAAE,MAAiC,CAAjCE,UAAiC,E;;QAC5ExB,mBAAA,CAA4F,aAAxFoB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjCG,UAAiC,E;;2BAMzEzB,mBAAA,CAgBK,aAfHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACN2B,WAGM,EACN1B,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAMK;IANDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAAmG,aAA/FoB,YAAA,CAA0FC,sBAAA;IAA7EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjCK,WAAiC,E;;QAChE3B,mBAAA,CAAuG,aAAnGoB,YAAA,CAA8FC,sBAAA;IAAjFC,EAAE,EAAC;EAAuB;sBAAE,MAAiC,CAAjCM,WAAiC,E;;QACxF5B,mBAAA,CAAsG,aAAlGoB,YAAA,CAA6FC,sBAAA;IAAhFC,EAAE,EAAC;EAAsB;sBAAE,MAAiC,CAAjCO,WAAiC,E;;2BASjF7B,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,gBAAgB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACpDd,KAAK,EAAC;MACN+B,WAGM,EACN9B,mBAAA,CAAoG;IAAjGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAIK;IAJDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAqBpB,EAAE,EAAC;MAE9FE,mBAAA,CAAyF,aAArFoB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCS,WAAiC,E;;6DAnD3Bf,KAAA,CAAAgB,IAAI,W,mBA2DjDhC,mBAAA,CAkHK,MAlHLiC,WAkHK,GAjHHjC,mBAAA,CAgBK,aAfHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNmC,WAGM,EACNlC,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAMK;IANDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAAyF,aAArFoB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCa,WAAiC,E;;QAClEnC,mBAAA,CAA4F,aAAxFoB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjCc,WAAiC,E;;QACrEpC,mBAAA,CAA6F,aAAzFoB,YAAA,CAAoFC,sBAAA;IAAvEC,EAAE,EAAC;EAAe;sBAAE,MAAiC,CAAjCe,WAAiC,E;;QACtErC,mBAAA,CAA4F,aAAxFoB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAY;sBAAE,MAAiC,CAAjCgB,WAAiC,E;;2BAIvEtC,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNwC,WAGM,EACNvC,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAKK;IALDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAAiG,aAA7FoB,YAAA,CAAwFC,sBAAA;IAA3EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjCkB,WAAiC,E;;QAC1ExC,mBAAA,CAAoG,aAAhGoB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAsB;sBAAE,MAAiC,CAAjCmB,WAAiC,E;;QAC7EzC,mBAAA,CAAqG,aAAjGoB,YAAA,CAA4FC,sBAAA;IAA/EC,EAAE,EAAC;EAAuB;sBAAE,MAAiC,CAAjCoB,WAAiC,E;;2BAIlF1C,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACN4C,WAGM,EACN3C,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAKK;IALDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAAgG,aAA5FoB,YAAA,CAAuFC,sBAAA;IAA1EC,EAAE,EAAC;EAAgB;sBAAE,MAAiC,CAAjCsB,WAAiC,E;;QACvE5C,mBAAA,CAAmG,aAA/FoB,YAAA,CAA0FC,sBAAA;IAA7EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjCuB,WAAiC,E;;QAC1E7C,mBAAA,CAAoG,aAAhGoB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjCwB,WAAiC,E;;2BAI/E9C,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNgD,WAGM,EACN/C,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAIK;IAJDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAA2F,aAAvFoB,YAAA,CAAkFC,sBAAA;IAArEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjC0B,WAAiC,E;;QAClEhD,mBAAA,CAA8F,aAA1FoB,YAAA,CAAqFC,sBAAA;IAAxEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjC2B,WAAiC,E;;2BAIzEjD,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNmD,WAGM,EACNlD,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAKK;IALDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAAmG,aAA/FoB,YAAA,CAA0FC,sBAAA;IAA7EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjC6B,WAAiC,E;;QAC1EnD,mBAAA,CAAsG,aAAlGoB,YAAA,CAA6FC,sBAAA;IAAhFC,EAAE,EAAC;EAAsB;sBAAE,MAAiC,CAAjC8B,WAAiC,E;;QAC7EpD,mBAAA,CAAoG,aAAhGoB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjC+B,WAAiC,E;;2BAI/ErD,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNuD,WAGM,EACNtD,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAKK;IALDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAAiG,aAA7FoB,YAAA,CAAwFC,sBAAA;IAA3EC,EAAE,EAAC;EAAiB;sBAAE,MAAiC,CAAjCiC,WAAiC,E;;QACxEvD,mBAAA,CAAoG,aAAhGoB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjCkC,WAAiC,E;;QAC3ExD,mBAAA,CAAqG,aAAjGoB,YAAA,CAA4FC,sBAAA;IAA/EC,EAAE,EAAC;EAAqB;sBAAE,MAAiC,CAAjCmC,WAAiC,E;;2BAKhFzD,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,gBAAgB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACpDd,KAAK,EAAC;MACN2D,WAGM,EACN1D,mBAAA,CAAoG;IAAjGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAIK;IAJDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAqBpB,EAAE,EAAC;MAE9FE,mBAAA,CAAyF,aAArFoB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCqC,WAAiC,E;;6DA7G3B3C,KAAA,CAAAgB,IAAI,Y,mBAsHjDhC,mBAAA,CAoFK,MApFL4D,WAoFK,GAlFH5D,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACN8D,WAGM,EACN7D,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAIK;IAJDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAE7FE,mBAAA,CAAqG,aAAjGoB,YAAA,CAA4FC,sBAAA;IAA/EC,EAAE,EAAC;EAAuB;sBAAE,MAAiC,CAAjCwC,WAAiC,E;;2BAK5E9D,mBAAA,CAcD,aAbHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNgE,WAGM,EACN/D,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAIK;IAJDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAA2F,aAAvFoB,YAAA,CAAkFC,sBAAA;IAArEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjC0C,WAAiC,E;;QAClEhE,mBAAA,CAA8F,aAA1FoB,YAAA,CAAqFC,sBAAA;IAAxEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjC2C,WAAiC,E;;2BAKzEjE,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNmE,WAGM,EACNlE,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAIK;IAJDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAC7FE,mBAAA,CAAgG,aAA5FoB,YAAA,CAAuFC,sBAAA;IAA1EC,EAAE,EAAC;EAAgB;sBAAE,MAAiC,CAAjC6C,WAAiC,E;;QACvEnE,mBAAA,CAAoG,aAAhGoB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjC8C,WAAiC,E;;2BAM/EpE,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNsE,WAGM,EACNrE,mBAAA,CAAmG;IAAhGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAIK;IAJDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBpB,EAAE,EAAC;MAE7FE,mBAAA,CAAuG,aAAnGoB,YAAA,CAA8FC,sBAAA;IAAjFC,EAAE,EAAC;EAAqB;sBAAE,MAAiC,CAAjCgD,WAAiC,E;;2BAKhFtE,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDO,IAAI,EAAC,gBAAgB;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACpDd,KAAK,EAAC;MACNwE,WAGM,EACNvE,mBAAA,CAAoG;IAAjGD,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7ElB,mBAAA,CAKK;IALDD,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAqBpB,EAAE,EAAC;MAEvFE,mBAAA,CAA4F,aAAxFoB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAY;sBAAE,MAAiC,CAAjCkD,WAAiC,E;;QAC1ExE,mBAAA,CAAyF,aAArFoB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCmD,WAAiC,E;;6DA/E3BzD,KAAA,CAAAgB,IAAI,U,GAuFjDhC,mBAAA,CAKM,OALN0E,WAKM,GAJJ1E,mBAAA,CAGI;IAHDO,IAAI,EAAC,qBAAqB;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAkE,IAAA,KAAE/D,QAAA,CAAAgE,IAAA,IAAAhE,QAAA,CAAAgE,IAAA,IAAAD,IAAA,CAAI;IAAE5E,KAAK,EAAC"}]}