{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749134881813}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["id", "_createElementVNode", "class", "style", "_hoisted_114", "_hoisted_115", "_createCommentVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "href", "onClick", "_cache", "_withModifiers", "$event", "$options", "toggleSubmenu", "_hoisted_4", "_normalizeClass", "$data", "openSubmenus", "includes", "show", "_createVNode", "_component_router_link", "to", "_hoisted_5", "_hoisted_7", "_hoisted_9", "_hoisted_11", "_hoisted_12", "_hoisted_14", "_hoisted_16", "_hoisted_18", "_hoisted_19", "role", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_25", "_hoisted_27", "_hoisted_29", "_hoisted_31", "_hoisted_32", "_hoisted_34", "_hoisted_36", "_hoisted_38", "_hoisted_39", "_hoisted_41", "_hoisted_43", "_hoisted_45", "_hoisted_46", "_hoisted_48", "_hoisted_50", "_hoisted_51", "_hoisted_53", "_hoisted_55", "_hoisted_57", "_hoisted_58", "_hoisted_60", "_hoisted_62", "_hoisted_64", "_hoisted_65", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_71", "_hoisted_73", "_hoisted_75", "_hoisted_77", "_hoisted_78", "_hoisted_80", "_hoisted_82", "_hoisted_84", "_hoisted_85", "_hoisted_87", "_hoisted_89", "_hoisted_91", "_hoisted_92", "_hoisted_94", "_hoisted_96", "_hoisted_97", "_hoisted_99", "_hoisted_101", "_hoisted_103", "_hoisted_104", "_hoisted_106", "_hoisted_108", "_hoisted_110", "_hoisted_111", "_hoisted_113", "args", "exit"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n  <!-- 侧边栏 -->\r\n  <nav id=\"sidebar\">\r\n    <div class=\"sidebar-header\">\r\n      <div class=\"d-flex align-items-center p-3\">\r\n        <i class=\"bi bi-shield-check text-white me-2\" style=\"font-size: 1.5rem\"></i>\r\n        <h5 class=\"mb-0\">小区物业管理系统</h5>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '管理员'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n\r\n\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '工作人员'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n          <li><router-link to=\"/usersInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu3\" @click.prevent=\"toggleSubmenu('userSubmenu3')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>公告管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu3') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu3') }\" id=\"userSubmenu3\">\r\n          <li><router-link to=\"/announcementsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage\"> <i class=\"bi bi-circle me-2\"></i>管理公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage2\"> <i class=\"bi bi-circle me-2\"></i>公告列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu4\" @click.prevent=\"toggleSubmenu('userSubmenu4')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>投诉建议管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu4') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu4') }\" id=\"userSubmenu4\">\r\n          <li><router-link to=\"/complaintsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage\"> <i class=\"bi bi-circle me-2\"></i>管理投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage2\"> <i class=\"bi bi-circle me-2\"></i>投诉建议列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu5\" @click.prevent=\"toggleSubmenu('userSubmenu5')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>房屋信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu5') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu5') }\" id=\"userSubmenu5\">\r\n          <li><router-link to=\"/houseAdd\"> <i class=\"bi bi-circle me-2\"></i>添加房屋信息</router-link></li>\r\n          <li><router-link to=\"/houseManage\"> <i class=\"bi bi-circle me-2\"></i>管理房屋信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu7\" @click.prevent=\"toggleSubmenu('userSubmenu7')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>费用信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu7') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu7') }\" id=\"userSubmenu7\">\r\n          <li><router-link to=\"/expenseinfoAdd\"> <i class=\"bi bi-circle me-2\"></i>添加费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage\"> <i class=\"bi bi-circle me-2\"></i>管理费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage2\"> <i class=\"bi bi-circle me-2\"></i>费用信息列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '用户'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n          <li><router-link to=\"/usersInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu3\" @click.prevent=\"toggleSubmenu('userSubmenu3')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>公告管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu3') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu3') }\" id=\"userSubmenu3\">\r\n          <li><router-link to=\"/announcementsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage\"> <i class=\"bi bi-circle me-2\"></i>管理公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage2\"> <i class=\"bi bi-circle me-2\"></i>公告列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu4\" @click.prevent=\"toggleSubmenu('userSubmenu4')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>投诉建议管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu4') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu4') }\" id=\"userSubmenu4\">\r\n          <li><router-link to=\"/complaintsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage\"> <i class=\"bi bi-circle me-2\"></i>管理投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage2\"> <i class=\"bi bi-circle me-2\"></i>投诉建议列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu5\" @click.prevent=\"toggleSubmenu('userSubmenu5')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>房屋信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu5') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu5') }\" id=\"userSubmenu5\">\r\n          <li><router-link to=\"/houseAdd\"> <i class=\"bi bi-circle me-2\"></i>添加房屋信息</router-link></li>\r\n          <li><router-link to=\"/houseManage\"> <i class=\"bi bi-circle me-2\"></i>管理房屋信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu7\" @click.prevent=\"toggleSubmenu('userSubmenu7')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>费用信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu7') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu7') }\" id=\"userSubmenu7\">\r\n          <li><router-link to=\"/expenseinfoAdd\"> <i class=\"bi bi-circle me-2\"></i>添加费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage\"> <i class=\"bi bi-circle me-2\"></i>管理费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage2\"> <i class=\"bi bi-circle me-2\"></i>费用信息列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n    <div class=\"sidebar-footer\">\r\n      <a href=\"javascript:void(0);\" @click=\"exit\" class=\"d-flex align-items-center text-white text-decoration-none p-3\">\r\n        <i class=\"bi bi-box-arrow-left me-2\"></i>\r\n        <span>退出登录</span>\r\n      </a>\r\n    </div>\r\n  </nav>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'LeftMenu',\r\n  data() {\r\n    return {\r\n      userLname: '',\r\n      role: '',\r\n      activeMenuItem: 'dashboard',\r\n      openSubmenus: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem('userLname');\r\n    this.role = sessionStorage.getItem('role');\r\n  },\r\n  methods: {\r\n    setActiveMenuItem(menuItem) {\r\n      this.activeMenuItem = menuItem;\r\n    },\r\n    toggleSubmenu(submenuId) {\r\n      const index = this.openSubmenus.indexOf(submenuId);\r\n      if (index === -1) {\r\n        // Close other submenus before opening new one\r\n        this.openSubmenus = [submenuId];\r\n      } else {\r\n        this.openSubmenus.splice(index, 1);\r\n      }\r\n    },\r\n    exit() {\r\n      this.$confirm('确认退出吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem('userLname');\r\n          sessionStorage.removeItem('role');\r\n          this.$router.push('/');\r\n        })\r\n        .catch(() => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.bi-chevron-down {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.rotate-180 {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.collapse {\r\n  transition: all 0.3s ease-out;\r\n}\r\n\r\n.collapse.show {\r\n  display: block;\r\n}\r\n\r\n.sub-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sub-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sub-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.left-menu {\r\n  width: 250px;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100vh;\r\n  z-index: 999;\r\n  background: #fff;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n#content {\r\n  width: calc(100% - 250px);\r\n  margin-left: 250px;\r\n  transition: all 0.3s;\r\n  min-height: 100vh;\r\n}\r\n\r\n:global(.sidebar-collapsed) #content {\r\n  margin-left: 0;\r\n  width: 100%;\r\n}\r\n\r\n:global(.sidebar-collapsed) .left-menu {\r\n  margin-left: -250px;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;EAEOA,EAAE,EAAC;AAAS;gEACfC,mBAAA,CAKM;EALDC,KAAK,EAAC;AAAgB,I,aACzBD,mBAAA,CAGM;EAHDC,KAAK,EAAC;AAA+B,I,aACxCD,mBAAA,CAA4E;EAAzEC,KAAK,EAAC,oCAAoC;EAACC,KAAyB,EAAzB;IAAA;EAAA;iBAC9CF,mBAAA,CAA8B;EAA1BC,KAAK,EAAC;AAAM,GAAC,UAAQ,E;;EAMzBA,KAAK,EAAC;AAA0B;gEAI9BD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAiB,cAAX,MAAI,E;gEAKqBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;iDAAK,MAAI;gEAClCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;iDAAK,MAAI;gEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAQ1ED,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;iEAK2BA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACzCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAWjFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAiB,cAAX,MAAI,E;iEAMqBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;;EAQxEA,KAAK,EAAC;AAA0B;iEAI9BD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAiB,cAAX,MAAI,E;iEAKqBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAClCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACxCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAOzED,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAiB,cAAX,MAAI,E;iEAK6BA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAClCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAOlFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;iEAKwBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACtCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAOjFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;iEAKmBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAO3ED,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;iEAK2BA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACzCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAOjFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;iEAKyBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACtCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAQlFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAiB,cAAX,MAAI,E;iEAMqBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;;EASxEA,KAAK,EAAC;AAA0B;iEAI9BD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAiB,cAAX,MAAI,E;iEAKqBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAClCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACxCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAOzED,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAiB,cAAX,MAAI,E;iEAK6BA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAClCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,MAAI;iEAOlFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;iEAKwBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACtCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAOjFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;iEAKmBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEAO3ED,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;iEAK2BA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;kDAAK,QAAM;iEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;mDAAK,QAAM;kEACzCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;mDAAK,QAAM;kEAOjFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAmB,cAAb,QAAM,E;kEAKyBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;mDAAK,QAAM;kEACpCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;mDAAK,QAAM;kEACtCD,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;mDAAK,QAAM;kEAQlFD,mBAAA,CAGM,c,aAFJA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB,I,aAC5BD,mBAAA,CAAiB,cAAX,MAAI,E;kEAMqBA,mBAAA,CAAiC;EAA9BC,KAAK,EAAC;AAAmB;mDAAK,MAAI;;EAQvEA,KAAK,EAAC;AAAgB;kEAEvBD,mBAAA,CAAyC;EAAtCC,KAAK,EAAC;AAA2B;kEACpCD,mBAAA,CAAiB,cAAX,MAAI;sBADVG,YAAyC,EACzCC,YAAiB,C;;;6DAlTvBC,mBAAA,SAAY,EACZL,mBAAA,CAoTM,OApTNM,UAoTM,GAnTJC,UAKM,E,gBAINP,mBAAA,CAsDK,MAtDLQ,UAsDK,GArDHR,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNe,UAGM,EACNhB,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAyF,aAArFsB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCC,UAAiC,E;;QAClEzB,mBAAA,CAA4F,aAAxFsB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjCE,UAAiC,E;;QACrE1B,mBAAA,CAA6F,aAAzFsB,YAAA,CAAoFC,sBAAA;IAAvEC,EAAE,EAAC;EAAe;sBAAE,MAAiC,CAAjCG,UAAiC,E;;2BAK1E3B,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACN2B,WAGM,EACN5B,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAmG,aAA/FsB,YAAA,CAA0FC,sBAAA;IAA7EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjCK,WAAiC,E;;QAC1E7B,mBAAA,CAAsG,aAAlGsB,YAAA,CAA6FC,sBAAA;IAAhFC,EAAE,EAAC;EAAsB;sBAAE,MAAiC,CAAjCM,WAAiC,E;;QAC7E9B,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjCO,WAAiC,E;;2BAQ/E/B,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,gBAAgB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACpDd,KAAK,EAAC;MACN+B,WAGM,EACNhC,mBAAA,CAAoG;IAAjGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAIK;IAJDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAqBrB,EAAE,EAAC;MAE9FC,mBAAA,CAAyF,aAArFsB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCS,WAAiC,E;;6DAjD3Bf,KAAA,CAAAgB,IAAI,W,mBAyDjDlC,mBAAA,CAkHK,MAlHLmC,WAkHK,GAjHHnC,mBAAA,CAgBK,aAfHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNmC,WAGM,EACNpC,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAMK;IANDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAyF,aAArFsB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCa,WAAiC,E;;QAClErC,mBAAA,CAA4F,aAAxFsB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjCc,WAAiC,E;;QACrEtC,mBAAA,CAA6F,aAAzFsB,YAAA,CAAoFC,sBAAA;IAAvEC,EAAE,EAAC;EAAe;sBAAE,MAAiC,CAAjCe,WAAiC,E;;QACtEvC,mBAAA,CAA4F,aAAxFsB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAY;sBAAE,MAAiC,CAAjCgB,WAAiC,E;;2BAIvExC,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNwC,WAGM,EACNzC,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAiG,aAA7FsB,YAAA,CAAwFC,sBAAA;IAA3EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjCkB,WAAiC,E;;QAC1E1C,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAsB;sBAAE,MAAiC,CAAjCmB,WAAiC,E;;QAC7E3C,mBAAA,CAAqG,aAAjGsB,YAAA,CAA4FC,sBAAA;IAA/EC,EAAE,EAAC;EAAuB;sBAAE,MAAiC,CAAjCoB,WAAiC,E;;2BAIlF5C,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACN4C,WAGM,EACN7C,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAgG,aAA5FsB,YAAA,CAAuFC,sBAAA;IAA1EC,EAAE,EAAC;EAAgB;sBAAE,MAAiC,CAAjCsB,WAAiC,E;;QACvE9C,mBAAA,CAAmG,aAA/FsB,YAAA,CAA0FC,sBAAA;IAA7EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjCuB,WAAiC,E;;QAC1E/C,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjCwB,WAAiC,E;;2BAI/EhD,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNgD,WAGM,EACNjD,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAIK;IAJDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAA2F,aAAvFsB,YAAA,CAAkFC,sBAAA;IAArEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjC0B,WAAiC,E;;QAClElD,mBAAA,CAA8F,aAA1FsB,YAAA,CAAqFC,sBAAA;IAAxEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjC2B,WAAiC,E;;2BAIzEnD,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNmD,WAGM,EACNpD,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAmG,aAA/FsB,YAAA,CAA0FC,sBAAA;IAA7EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjC6B,WAAiC,E;;QAC1ErD,mBAAA,CAAsG,aAAlGsB,YAAA,CAA6FC,sBAAA;IAAhFC,EAAE,EAAC;EAAsB;sBAAE,MAAiC,CAAjC8B,WAAiC,E;;QAC7EtD,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjC+B,WAAiC,E;;2BAI/EvD,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNuD,WAGM,EACNxD,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAiG,aAA7FsB,YAAA,CAAwFC,sBAAA;IAA3EC,EAAE,EAAC;EAAiB;sBAAE,MAAiC,CAAjCiC,WAAiC,E;;QACxEzD,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjCkC,WAAiC,E;;QAC3E1D,mBAAA,CAAqG,aAAjGsB,YAAA,CAA4FC,sBAAA;IAA/EC,EAAE,EAAC;EAAqB;sBAAE,MAAiC,CAAjCmC,WAAiC,E;;2BAKhF3D,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,gBAAgB;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACpDd,KAAK,EAAC;MACN2D,WAGM,EACN5D,mBAAA,CAAoG;IAAjGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAIK;IAJDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAqBrB,EAAE,EAAC;MAE9FC,mBAAA,CAAyF,aAArFsB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCqC,WAAiC,E;;6DA7G3B3C,KAAA,CAAAgB,IAAI,Y,mBAsHjDlC,mBAAA,CAkHK,MAlHL8D,WAkHK,GAjHH9D,mBAAA,CAgBK,aAfHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACN8D,WAGM,EACN/D,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAMK;IANDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAyF,aAArFsB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCwC,WAAiC,E;;QAClEhE,mBAAA,CAA4F,aAAxFsB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjCyC,WAAiC,E;;QACrEjE,mBAAA,CAA6F,aAAzFsB,YAAA,CAAoFC,sBAAA;IAAvEC,EAAE,EAAC;EAAe;sBAAE,MAAiC,CAAjC0C,WAAiC,E;;QACtElE,mBAAA,CAA4F,aAAxFsB,YAAA,CAAmFC,sBAAA;IAAtEC,EAAE,EAAC;EAAY;sBAAE,MAAiC,CAAjC2C,WAAiC,E;;2BAIvEnE,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNmE,WAGM,EACNpE,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAiG,aAA7FsB,YAAA,CAAwFC,sBAAA;IAA3EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjC6C,WAAiC,E;;QAC1ErE,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAsB;sBAAE,MAAiC,CAAjC8C,WAAiC,E;;QAC7EtE,mBAAA,CAAqG,aAAjGsB,YAAA,CAA4FC,sBAAA;IAA/EC,EAAE,EAAC;EAAuB;sBAAE,MAAiC,CAAjC+C,WAAiC,E;;2BAIlFvE,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNuE,WAGM,EACNxE,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAgG,aAA5FsB,YAAA,CAAuFC,sBAAA;IAA1EC,EAAE,EAAC;EAAgB;sBAAE,MAAiC,CAAjCiD,WAAiC,E;;QACvEzE,mBAAA,CAAmG,aAA/FsB,YAAA,CAA0FC,sBAAA;IAA7EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjCkD,WAAiC,E;;QAC1E1E,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjCmD,WAAiC,E;;2BAI/E3E,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACN2E,WAGM,EACN5E,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAIK;IAJDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAA2F,aAAvFsB,YAAA,CAAkFC,sBAAA;IAArEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCqD,WAAiC,E;;QAClE7E,mBAAA,CAA8F,aAA1FsB,YAAA,CAAqFC,sBAAA;IAAxEC,EAAE,EAAC;EAAc;sBAAE,MAAiC,CAAjCsD,WAAiC,E;;2BAIzE9E,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACN8E,WAGM,EACN/E,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAmG,aAA/FsB,YAAA,CAA0FC,sBAAA;IAA7EC,EAAE,EAAC;EAAmB;sBAAE,MAAiC,CAAjCwD,WAAiC,E;;QAC1EhF,mBAAA,CAAsG,aAAlGsB,YAAA,CAA6FC,sBAAA;IAAhFC,EAAE,EAAC;EAAsB;sBAAE,MAAiC,CAAjCyD,WAAiC,E;;QAC7EjF,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjC0D,YAAiC,E;;2BAI/ElF,mBAAA,CAeK,aAdHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,eAAe;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACnDd,KAAK,EAAC;MACNkF,YAGM,EACNnF,mBAAA,CAAmG;IAAhGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAKK;IALDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAoBrB,EAAE,EAAC;MAC7FC,mBAAA,CAAiG,aAA7FsB,YAAA,CAAwFC,sBAAA;IAA3EC,EAAE,EAAC;EAAiB;sBAAE,MAAiC,CAAjC4D,YAAiC,E;;QACxEpF,mBAAA,CAAoG,aAAhGsB,YAAA,CAA2FC,sBAAA;IAA9EC,EAAE,EAAC;EAAoB;sBAAE,MAAiC,CAAjC6D,YAAiC,E;;QAC3ErF,mBAAA,CAAqG,aAAjGsB,YAAA,CAA4FC,sBAAA;IAA/EC,EAAE,EAAC;EAAqB;sBAAE,MAAiC,CAAjC8D,YAAiC,E;;2BAKhFtF,mBAAA,CAcK,aAbHA,mBAAA,CAOI;IAPDS,IAAI,EAAC,gBAAgB;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAAC,MAAA,IAAUC,QAAA,CAAAC,aAAa;IACpDd,KAAK,EAAC;MACNsF,YAGM,EACNvF,mBAAA,CAAoG;IAAjGC,KAAK,EAAAgB,eAAA,EAAC,oBAAoB;MAAA,cAAyBC,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;6BAE7EpB,mBAAA,CAIK;IAJDC,KAAK,EAAAgB,eAAA,EAAC,wBAAwB;MAAAI,IAAA,EAAiBH,KAAA,CAAAC,YAAY,CAACC,QAAQ;IAAA;IAAqBrB,EAAE,EAAC;MAE9FC,mBAAA,CAAyF,aAArFsB,YAAA,CAAgFC,sBAAA;IAAnEC,EAAE,EAAC;EAAW;sBAAE,MAAiC,CAAjCgE,YAAiC,E;;6DA7G3BtE,KAAA,CAAAgB,IAAI,U,GAqHjDlC,mBAAA,CAKM,OALNyF,YAKM,GAJJzF,mBAAA,CAGI;IAHDS,IAAI,EAAC,qBAAqB;IAAEC,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAA+E,IAAA,KAAE5E,QAAA,CAAA6E,IAAA,IAAA7E,QAAA,CAAA6E,IAAA,IAAAD,IAAA,CAAI;IAAEzF,KAAK,EAAC"}]}