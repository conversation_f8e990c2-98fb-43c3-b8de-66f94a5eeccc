package com.service;
import java.util.List;

import com.model.Expenseinfo;
import com.util.PageBean;

public interface ExpenseinfoService{
	
	//查询多条记录
	public List<Expenseinfo> queryExpenseinfoList(Expenseinfo expenseinfo,PageBean page) throws Exception;
 
	//添加
	public int insertExpenseinfo(Expenseinfo expenseinfo) throws Exception ;
	
	//根据ID删除
	public int deleteExpenseinfo(int id) throws Exception ;
	
	//更新
	public int updateExpenseinfo(Expenseinfo expenseinfo) throws Exception ;
	
	//根据ID查询单条数据
	public Expenseinfo queryExpenseinfoById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Expenseinfo expenseinfo);

}

