package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/announcements")
public class AnnouncementsController{
	
	@Resource
	private AnnouncementsService announcementsService;
	
	//公告列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Announcements>> list(@RequestBody Announcements announcements, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = announcementsService.getCount(announcements);
		//获取当前页记录
		List<Announcements> announcementsList = announcementsService.queryAnnouncementsList(announcements, page);
		//遍历
		for (Announcements announcements2 : announcementsList) {
			announcements2.setAdetail(removeHTML.Html2Text(announcements2.getAdetail()));

		}

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(announcementsList, counts, page_count);
	}
        
	//添加公告
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Announcements announcements, HttpServletRequest req) throws Exception {
		try {
			announcementsService.insertAnnouncements(announcements); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除公告
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			announcementsService.deleteAnnouncements(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改公告
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Announcements announcements, HttpServletRequest req) throws Exception {
		try {
			announcementsService.updateAnnouncements(announcements); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回公告详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Announcements announcements=announcementsService.queryAnnouncementsById(id); //根据ID查询
			return Response.success(announcements);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

