{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\Header.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\Header.vue", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCB7IEVsRHJvcGRvd24sIEVsRHJvcGRvd25NZW51LCBFbERyb3Bkb3duSXRlbSB9IGZyb20gJ2VsZW1lbnQtcGx1cyc7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBFbERyb3Bkb3duLAogICAgRWxEcm9wZG93bk1lbnUsCiAgICBFbERyb3Bkb3duSXRlbQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVzZXJMbmFtZTogJycsCiAgICAgIHJvbGU6ICcnLAogICAgICBjdXJyZW50VGltZTogJycsCiAgICAgIHRpbWVyOiBudWxsCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMudXNlckxuYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndXNlckxuYW1lJyk7CiAgICB0aGlzLnJvbGUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdyb2xlJyk7CiAgICB0aGlzLnVwZGF0ZVRpbWUoKTsKICAgIHRoaXMudGltZXIgPSBzZXRJbnRlcnZhbCh0aGlzLnVwZGF0ZVRpbWUsIDEwMDApOwogIH0sCiAgYmVmb3JlVW5tb3VudCgpIHsKICAgIGlmICh0aGlzLnRpbWVyKSB7CiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy50aW1lcik7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICB1cGRhdGVUaW1lKCkgewogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCB5ZWFyID0gbm93LmdldEZ1bGxZZWFyKCk7CiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKG5vdy5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgY29uc3QgZGF0ZSA9IFN0cmluZyhub3cuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOwogICAgICBjb25zdCBob3VycyA9IFN0cmluZyhub3cuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgY29uc3QgbWludXRlcyA9IFN0cmluZyhub3cuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpOwogICAgICBjb25zdCBzZWNvbmRzID0gU3RyaW5nKG5vdy5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIGNvbnN0IHdlZWtEYXlzID0gWyfmmJ/mnJ/ml6UnLCAn5pif5pyf5LiAJywgJ+aYn+acn+S6jCcsICfmmJ/mnJ/kuIknLCAn5pif5pyf5ZubJywgJ+aYn+acn+S6lCcsICfmmJ/mnJ/lha0nXTsKICAgICAgY29uc3Qgd2Vla0RheSA9IHdlZWtEYXlzW25vdy5nZXREYXkoKV07CiAgICAgIHRoaXMuY3VycmVudFRpbWUgPSBgJHt5ZWFyfeW5tCR7bW9udGh95pyIJHtkYXRlfeaXpSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc30gJHt3ZWVrRGF5fWA7CiAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjdXJyZW50VGltZScpLnRleHRDb250ZW50ID0gdGhpcy5jdXJyZW50VGltZTsKICAgIH0sCiAgICBleGl0KCkgewogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTpgIDlh7rlkJc/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXJMbmFtZScpOwogICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ3JvbGUnKTsKICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnLycpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["ElDropdown", "ElDropdownMenu", "ElDropdownItem", "components", "data", "userLname", "role", "currentTime", "timer", "mounted", "sessionStorage", "getItem", "updateTime", "setInterval", "beforeUnmount", "clearInterval", "methods", "now", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "date", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "weekDays", "weekDay", "getDay", "document", "getElementById", "textContent", "exit", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "$router", "push", "catch"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\Header.vue"], "sourcesContent": ["<template>\r\n  <nav class=\"navbar navbar-expand-lg navbar-light bg-white\">\r\n    <div class=\"container-fluid\">\r\n      <div class=\"ms-auto d-flex align-items-center\">\r\n        <div class=\"current-time me-4\">\r\n          <i class=\"bi bi-clock me-2\"></i>\r\n          <span id=\"currentTime\"></span>\r\n        </div>\r\n        <el-dropdown trigger=\"click\">\r\n          <div class=\"el-dropdown-link d-flex align-items-center\" style=\"cursor: pointer\">\r\n            <img\r\n                src=\"../assets/images/touxiang.png\"\r\n                class=\"rounded-circle me-2\"\r\n                style=\"width: 32px; height: 32px; object-fit: cover\"\r\n            />\r\n            <span>{{ role }}</span>\r\n          </div>\r\n          <template #dropdown>\r\n            <el-dropdown-menu>\r\n              <el-dropdown-item>\r\n                <router-link to=\"/\" class=\"dropdown-link\" target=\"_blank\">\r\n                  <i class=\"bi bi-house me-2\"></i>网站首页\r\n                </router-link>\r\n              </el-dropdown-item>\r\n              <el-dropdown-item>\r\n                <router-link to=\"/password\" class=\"dropdown-link\">\r\n                  <i class=\"bi bi-key me-2\"></i>修改密码\r\n                </router-link>\r\n              </el-dropdown-item>\r\n              <el-dropdown-item divided @click=\"exit\">\r\n                <i class=\"bi bi-box-arrow-right me-2\"></i>退出登录\r\n              </el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </template>\r\n        </el-dropdown>\r\n      </div>\r\n    </div>\r\n  </nav>\r\n</template>\r\n\r\n<script>\r\nimport { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus';\r\n\r\nexport default {\r\n  components: {\r\n    ElDropdown,\r\n    ElDropdownMenu,\r\n    ElDropdownItem,\r\n  },\r\n  data() {\r\n    return {\r\n      userLname: '',\r\n      role: '',\r\n      currentTime: '',\r\n      timer: null,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem('userLname');\r\n    this.role = sessionStorage.getItem('role');\r\n    this.updateTime();\r\n    this.timer = setInterval(this.updateTime, 1000);\r\n  },\r\n  beforeUnmount() {\r\n    if (this.timer) {\r\n      clearInterval(this.timer);\r\n    }\r\n  },\r\n  methods: {\r\n    updateTime() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      const date = String(now.getDate()).padStart(2, '0');\r\n      const hours = String(now.getHours()).padStart(2, '0');\r\n      const minutes = String(now.getMinutes()).padStart(2, '0');\r\n      const seconds = String(now.getSeconds()).padStart(2, '0');\r\n      const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];\r\n      const weekDay = weekDays[now.getDay()];\r\n\r\n      this.currentTime = `${year}年${month}月${date}日 ${hours}:${minutes}:${seconds} ${weekDay}`;\r\n      document.getElementById('currentTime').textContent = this.currentTime;\r\n    },\r\n    exit() {\r\n      this.$confirm('确认退出吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n          .then(() => {\r\n            sessionStorage.removeItem('userLname');\r\n            sessionStorage.removeItem('role');\r\n            this.$router.push('/');\r\n          })\r\n          .catch(() => {\r\n          });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.navbar {\r\n  background: white;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1050;\r\n}\r\n\r\n.current-time {\r\n  font-size: 0.95rem;\r\n  color: #666;\r\n  min-width: 300px;\r\n  text-align: right;\r\n}\r\n\r\n.dropdown-link {\r\n  text-decoration: none;\r\n  color: inherit;\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n:deep(.el-dropdown-menu__item) {\r\n  padding: 8px 16px;\r\n  line-height: 1.5;\r\n  min-width: 120px;\r\n}\r\n\r\n:deep(.el-dropdown-menu__item i) {\r\n  margin-right: 8px;\r\n}\r\n\r\n:deep(.el-popper) {\r\n  min-width: 150px;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";AAyCA,SAASA,UAAU,EAAEC,cAAc,EAAEC,cAAa,QAAS,cAAc;AAEzE,eAAe;EACbC,UAAU,EAAE;IACVH,UAAU;IACVC,cAAc;IACdC;EACF,CAAC;EACDE,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACJ,SAAQ,GAAIK,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACL,IAAG,GAAII,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IAC1C,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACJ,KAAI,GAAIK,WAAW,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAAC;EACjD,CAAC;EACDE,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACN,KAAK,EAAE;MACdO,aAAa,CAAC,IAAI,CAACP,KAAK,CAAC;IAC3B;EACF,CAAC;EACDQ,OAAO,EAAE;IACPJ,UAAUA,CAAA,EAAG;MACX,MAAMK,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;MACtB,MAAMC,IAAG,GAAIF,GAAG,CAACG,WAAW,CAAC,CAAC;MAC9B,MAAMC,KAAI,GAAIC,MAAM,CAACL,GAAG,CAACM,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACzD,MAAMC,IAAG,GAAIH,MAAM,CAACL,GAAG,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMG,KAAI,GAAIL,MAAM,CAACL,GAAG,CAACW,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACrD,MAAMK,OAAM,GAAIP,MAAM,CAACL,GAAG,CAACa,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACzD,MAAMO,OAAM,GAAIT,MAAM,CAACL,GAAG,CAACe,UAAU,CAAC,CAAC,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACzD,MAAMS,QAAO,GAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAClE,MAAMC,OAAM,GAAID,QAAQ,CAAChB,GAAG,CAACkB,MAAM,CAAC,CAAC,CAAC;MAEtC,IAAI,CAAC5B,WAAU,GAAK,GAAEY,IAAK,IAAGE,KAAM,IAAGI,IAAK,KAAIE,KAAM,IAAGE,OAAQ,IAAGE,OAAO,IAAIG,OAAQ,EAAC;MACxFE,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,CAACC,WAAU,GAAI,IAAI,CAAC/B,WAAW;IACvE,CAAC;IACDgC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACIC,IAAI,CAAC,MAAM;QACVlC,cAAc,CAACmC,UAAU,CAAC,WAAW,CAAC;QACtCnC,cAAc,CAACmC,UAAU,CAAC,MAAM,CAAC;QACjC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;MACxB,CAAC,EACAC,KAAK,CAAC,MAAM,CACb,CAAC,CAAC;IACR;EACF;AACF,CAAC"}]}