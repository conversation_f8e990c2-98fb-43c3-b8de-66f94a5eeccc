{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffAdd.vue?vue&type=template&id=e23c0a58", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffAdd.vue", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCB3aXRoQ3R4IGFzIF93aXRoQ3R4LCBjcmVhdGVUZXh0Vk5vZGUgYXMgX2NyZWF0ZVRleHRWTm9kZSwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICBzdHlsZTogewogICAgIndpZHRoIjogIjEwMCUiLAogICAgImxpbmUtaGVpZ2h0IjogIjMwcHgiLAogICAgInRleHQtYWxpZ24iOiAibGVmdCIKICB9Cn07CmNvbnN0IF9ob2lzdGVkXzIgPSAvKiNfX1BVUkVfXyovX2NyZWF0ZVRleHRWTm9kZSgiIOeUtyAiKTsKY29uc3QgX2hvaXN0ZWRfMyA9IC8qI19fUFVSRV9fKi9fY3JlYXRlVGV4dFZOb2RlKCIg5aWzICIpOwpjb25zdCBfaG9pc3RlZF80ID0gLyojX19QVVJFX18qL19jcmVhdGVUZXh0Vk5vZGUoIuaPkCDkuqQiKTsKY29uc3QgX2hvaXN0ZWRfNSA9IC8qI19fUFVSRV9fKi9fY3JlYXRlVGV4dFZOb2RlKCLov5Qg5ZueIik7CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9pbnB1dCA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1pbnB1dCIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfZm9ybV9pdGVtID0gX3Jlc29sdmVDb21wb25lbnQoImVsLWZvcm0taXRlbSIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfcmFkaW8gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtcmFkaW8iKTsKICBjb25zdCBfY29tcG9uZW50X2VsX3JhZGlvX2dyb3VwID0gX3Jlc29sdmVDb21wb25lbnQoImVsLXJhZGlvLWdyb3VwIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9idXR0b24gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtYnV0dG9uIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9mb3JtID0gX3Jlc29sdmVDb21wb25lbnQoImVsLWZvcm0iKTsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xLCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybSwgewogICAgbW9kZWw6ICRkYXRhLmZvcm1EYXRhLAogICAgImxhYmVsLXdpZHRoIjogIjIwJSIsCiAgICByZWY6ICJmb3JtRGF0YVJlZiIsCiAgICBydWxlczogJGRhdGEuYWRkcnVsZXMsCiAgICBhbGlnbjogImxlZnQiCiAgfSwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIueUqOaIt+WQjSIsCiAgICAgIHByb3A6ICJwbG5hbWUiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9pbnB1dCwgewogICAgICAgIG1vZGVsVmFsdWU6ICRkYXRhLmZvcm1EYXRhLnBsbmFtZSwKICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVswXSB8fCAoX2NhY2hlWzBdID0gJGV2ZW50ID0+ICRkYXRhLmZvcm1EYXRhLnBsbmFtZSA9ICRldmVudCksCiAgICAgICAgcGxhY2Vob2xkZXI6ICLnlKjmiLflkI0iLAogICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAid2lkdGgiOiAiNTAlIgogICAgICAgIH0KICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgIGxhYmVsOiAi55m75b2V5a+G56CBIiwKICAgICAgcHJvcDogInB3b3JkIgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5wd29yZCwKICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsxXSB8fCAoX2NhY2hlWzFdID0gJGV2ZW50ID0+ICRkYXRhLmZvcm1EYXRhLnB3b3JkID0gJGV2ZW50KSwKICAgICAgICBwbGFjZWhvbGRlcjogIueZu+W9leWvhueggSIsCiAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICJ3aWR0aCI6ICI1MCUiCiAgICAgICAgfQogICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgbGFiZWw6ICLlp5PlkI0iLAogICAgICBwcm9wOiAicG5hbWUiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9pbnB1dCwgewogICAgICAgIG1vZGVsVmFsdWU6ICRkYXRhLmZvcm1EYXRhLnBuYW1lLAogICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzJdIHx8IChfY2FjaGVbMl0gPSAkZXZlbnQgPT4gJGRhdGEuZm9ybURhdGEucG5hbWUgPSAkZXZlbnQpLAogICAgICAgIHBsYWNlaG9sZGVyOiAi5aeT5ZCNIiwKICAgICAgICBzdHlsZTogewogICAgICAgICAgIndpZHRoIjogIjUwJSIKICAgICAgICB9CiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIuaAp+WIqyIsCiAgICAgIHByb3A6ICJnZW5kZXIiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9yYWRpb19ncm91cCwgewogICAgICAgIG1vZGVsVmFsdWU6ICRkYXRhLmZvcm1EYXRhLmdlbmRlciwKICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVszXSB8fCAoX2NhY2hlWzNdID0gJGV2ZW50ID0+ICRkYXRhLmZvcm1EYXRhLmdlbmRlciA9ICRldmVudCkKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9yYWRpbywgewogICAgICAgICAgbGFiZWw6ICLnlLciCiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19ob2lzdGVkXzJdKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3JhZGlvLCB7CiAgICAgICAgICBsYWJlbDogIuWlsyIKICAgICAgICB9LCB7CiAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2hvaXN0ZWRfM10pLAogICAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgICB9KV0pLAoKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9LCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgbGFiZWw6ICLlubTpvoQiLAogICAgICBwcm9wOiAiYWdlIgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5hZ2UsCiAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbNF0gfHwgKF9jYWNoZVs0XSA9ICRldmVudCA9PiAkZGF0YS5mb3JtRGF0YS5hZ2UgPSAkZXZlbnQpLAogICAgICAgIHBsYWNlaG9sZGVyOiAi5bm06b6EIiwKICAgICAgICBzdHlsZTogewogICAgICAgICAgIndpZHRoIjogIjUwJSIKICAgICAgICB9CiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICBsYWJlbDogIuiBlOezu+aWueW8jyIsCiAgICAgIHByb3A6ICJjb250YWN0IgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5jb250YWN0LAogICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzVdIHx8IChfY2FjaGVbNV0gPSAkZXZlbnQgPT4gJGRhdGEuZm9ybURhdGEuY29udGFjdCA9ICRldmVudCksCiAgICAgICAgcGxhY2Vob2xkZXI6ICLogZTns7vmlrnlvI8iLAogICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAid2lkdGgiOiAiNTAlIgogICAgICAgIH0KICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgIGxhYmVsOiAi6IGU57O75Zyw5Z2AIiwKICAgICAgcHJvcDogImFkZHJlc3MiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9pbnB1dCwgewogICAgICAgIG1vZGVsVmFsdWU6ICRkYXRhLmZvcm1EYXRhLmFkZHJlc3MsCiAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbNl0gfHwgKF9jYWNoZVs2XSA9ICRldmVudCA9PiAkZGF0YS5mb3JtRGF0YS5hZGRyZXNzID0gJGV2ZW50KSwKICAgICAgICBwbGFjZWhvbGRlcjogIuiBlOezu+WcsOWdgCIsCiAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICJ3aWR0aCI6ICI1MCUiCiAgICAgICAgfQogICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgbGFiZWw6ICLlrqHmoLjnirbmgIEiLAogICAgICBwcm9wOiAic3RhdHVzIgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICBtb2RlbFZhbHVlOiAkZGF0YS5mb3JtRGF0YS5zdGF0dXMsCiAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbN10gfHwgKF9jYWNoZVs3XSA9ICRldmVudCA9PiAkZGF0YS5mb3JtRGF0YS5zdGF0dXMgPSAkZXZlbnQpLAogICAgICAgIHBsYWNlaG9sZGVyOiAi5a6h5qC454q25oCBIiwKICAgICAgICBzdHlsZTogewogICAgICAgICAgIndpZHRoIjogIjUwJSIKICAgICAgICB9CiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgbnVsbCwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgICAgdHlwZTogInByaW1hcnkiLAogICAgICAgIHNpemU6ICJzbWFsbCIsCiAgICAgICAgb25DbGljazogJG9wdGlvbnMuc2F2ZSwKICAgICAgICBsb2FkaW5nOiAkZGF0YS5idG5Mb2FkaW5nLAogICAgICAgIGljb246ICJlbC1pY29uLXVwbG9hZCIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfaG9pc3RlZF80XSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJvbkNsaWNrIiwgImxvYWRpbmciXSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICBzaXplOiAic21hbGwiLAogICAgICAgIG9uQ2xpY2s6ICRvcHRpb25zLmdvQmFjaywKICAgICAgICBpY29uOiAiZWwtaWNvbi1iYWNrIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19ob2lzdGVkXzVdKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9LCA4IC8qIFBST1BTICovLCBbIm9uQ2xpY2siXSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pXSksCgogICAgXzogMSAvKiBTVEFCTEUgKi8KICB9LCA4IC8qIFBST1BTICovLCBbIm1vZGVsIiwgInJ1bGVzIl0pXSk7Cn0="}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_input", "plname", "$event", "placeholder", "pword", "pname", "_component_el_radio_group", "gender", "_component_el_radio", "age", "contact", "address", "status", "_component_el_button", "type", "size", "onClick", "$options", "save", "loading", "btnLoading", "icon", "goBack"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffAdd.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"用户名\" prop=\"plname\">\r\n<el-input v-model=\"formData.plname\" placeholder=\"用户名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"登录密码\" prop=\"pword\">\r\n<el-input v-model=\"formData.pword\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"pname\">\r\n<el-input v-model=\"formData.pname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"gender\">\r\n<el-radio-group v-model=\"formData.gender\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"年龄\" prop=\"age\">\r\n<el-input v-model=\"formData.age\" placeholder=\"年龄\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系方式\" prop=\"contact\">\r\n<el-input v-model=\"formData.contact\" placeholder=\"联系方式\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系地址\" prop=\"address\">\r\n<el-input v-model=\"formData.address\" placeholder=\"联系地址\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"审核状态\" prop=\"status\">\r\n<el-input v-model=\"formData.status\" placeholder=\"审核状态\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'PropertystaffAdd',\n  components: {\n    \n  },  \n    data() {\n      return {   \n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          plname: [{ required: true, message: '请输入用户名', trigger: 'blur' },\r\n],          pword: [{ required: true, message: '请输入登录密码', trigger: 'blur' },\r\n],          pname: [{ required: true, message: '请输入姓名', trigger: 'blur' },\r\n],          gender: [{ required: true, message: '请输入性别', trigger: 'blur' },\r\n],          age: [{ required: true, message: '请输入年龄', trigger: 'blur' },\r\n],          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },\r\n],          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },\r\n],          status: [{ required: true, message: '请输入审核状态', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    mounted() {\r\n    \r\n    },\r\n\r\n \n    methods: {    \n   // 添加\n    save() {       \n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n           if (valid) {\n             let url = base + \"/propertystaff/add\";\n             this.btnLoading = true;\n             request.post(url, this.formData).then((res) => { //发送请求         \n               if (res.code == 200) {\n                 this.$message({\n                   message: \"操作成功\",\n                   type: \"success\",\n                   offset: 320,\n                 });              \n                this.$router.push({\n                path: \"/PropertystaffManage\",\n                });\n               } else {\n                 this.$message({\n                   message: res.msg,\n                   type: \"error\",\n                   offset: 320,\n                 });\n               }\n               this.btnLoading=false;\n             });\n           }        \n           \n         });\n    },\n    \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/PropertystaffManage\",\n          });\n        },       \n              \n          \n           \n           \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;iDAa5C,KAEpB;iDACoB,KAEpB;iDAgBiG,KAAG;iDAC5B,KAAG;;;;;;;;uBAnCvEC,mBAAA,CAwCM,OAxCNC,UAwCM,GAvCHC,YAAA,CAoCGC,kBAAA;IApCOC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAC/F,MAEe,CAFfR,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC/B,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACS,MAAM;mEAAfV,KAAA,CAAAC,QAAQ,CAACS,MAAM,GAAAC,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAsF,CAAtFX,YAAA,CAAsFY,mBAAA;oBAAnET,KAAA,CAAAC,QAAQ,CAACY,KAAK;mEAAdb,KAAA,CAAAC,QAAQ,CAACY,KAAK,GAAAF,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEvDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAoF,CAApFX,YAAA,CAAoFY,mBAAA;oBAAjET,KAAA,CAAAC,QAAQ,CAACa,KAAK;mEAAdd,KAAA,CAAAC,QAAQ,CAACa,KAAK,GAAAH,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAErDG,YAAA,CASeS,uBAAA;MATDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAOiB,CAPjBX,YAAA,CAOiBkB,yBAAA;oBAPQf,KAAA,CAAAC,QAAQ,CAACe,MAAM;mEAAfhB,KAAA,CAAAC,QAAQ,CAACe,MAAM,GAAAL,MAAA;;0BACxC,MAEW,CAFXd,YAAA,CAEWoB,mBAAA;UAFDV,KAAK,EAAC;QAAG;4BAAC,MAEpB,C;;YACAV,YAAA,CAEWoB,mBAAA;UAFDV,KAAK,EAAC;QAAG;4BAAC,MAEpB,C;;;;;;;QAGAV,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAkF,CAAlFX,YAAA,CAAkFY,mBAAA;oBAA/DT,KAAA,CAAAC,QAAQ,CAACiB,GAAG;mEAAZlB,KAAA,CAAAC,QAAQ,CAACiB,GAAG,GAAAP,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEnDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAwF,CAAxFX,YAAA,CAAwFY,mBAAA;oBAArET,KAAA,CAAAC,QAAQ,CAACkB,OAAO;mEAAhBnB,KAAA,CAAAC,QAAQ,CAACkB,OAAO,GAAAR,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEzDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAwF,CAAxFX,YAAA,CAAwFY,mBAAA;oBAArET,KAAA,CAAAC,QAAQ,CAACmB,OAAO;mEAAhBpB,KAAA,CAAAC,QAAQ,CAACmB,OAAO,GAAAT,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEzDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAAuF,CAAvFX,YAAA,CAAuFY,mBAAA;oBAApET,KAAA,CAAAC,QAAQ,CAACoB,MAAM;mEAAfrB,KAAA,CAAAC,QAAQ,CAACoB,MAAM,GAAAV,MAAA;QAAEC,WAAW,EAAC,MAAM;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAExDG,YAAA,CAGeS,uBAAA;wBAFf,MAAgH,CAAhHT,YAAA,CAAgHyB,oBAAA;QAArGC,IAAI,EAAC,SAAS;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAC,IAAI;QAAGC,OAAO,EAAE5B,KAAA,CAAA6B,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAG,C;;iDACpGjC,YAAA,CAAuFyB,oBAAA;QAA5EC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,OAAO;QAAEC,OAAK,EAAEC,QAAA,CAAAK,MAAM;QAAED,IAAI,EAAC;;0BAAe,MAAG,C"}]}