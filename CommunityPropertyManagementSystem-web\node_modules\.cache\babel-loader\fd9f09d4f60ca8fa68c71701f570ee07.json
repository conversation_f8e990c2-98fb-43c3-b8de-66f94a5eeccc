{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffInfo.vue", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "pname", "required", "message", "trigger", "gender", "age", "contact", "address", "status", "created", "user", "JSON", "parse", "sessionStorage", "getItem", "pid", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "stringify", "resdata", "save", "$refs", "validate", "valid", "code", "$message", "type", "offset", "msg"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffInfo.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"用户名\" prop=\"plname\">\r\n<el-input v-model=\"formData.plname\" placeholder=\"用户名\"  style=\"width:50%;\" disabled ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"pname\">\r\n<el-input v-model=\"formData.pname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"gender\">\r\n<el-radio-group v-model=\"formData.gender\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"年龄\" prop=\"age\">\r\n<el-input v-model=\"formData.age\" placeholder=\"年龄\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系方式\" prop=\"contact\">\r\n<el-input v-model=\"formData.contact\" placeholder=\"联系方式\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系地址\" prop=\"address\">\r\n<el-input v-model=\"formData.address\" placeholder=\"联系地址\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"审核状态\" prop=\"status\">\r\n<el-input v-model=\"formData.status\" placeholder=\"审核状态\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'PropertystaffInfo',\n  components: {\n    \n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          pname: [{ required: true, message: '请输入姓名', trigger: 'blur' },\r\n],          gender: [{ required: true, message: '请输入性别', trigger: 'blur' },\r\n],          age: [{ required: true, message: '请输入年龄', trigger: 'blur' },\r\n],          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },\r\n],          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },\r\n],          status: [{ required: true, message: '请输入审核状态', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n     created() {\n        var user = JSON.parse(sessionStorage.getItem(\"user\"));\n        this.id = user.pid;\n        this.getDatas();\n      }, \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/propertystaff/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            \n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/propertystaff/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });                 \n                } else {\n                  this.$message({\n                    message: res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n       \n              \n          \n           \n           \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": "AAyCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AAEtD,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACtE;QAAWC,MAAM,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACzE;QAAWE,GAAG,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CACtE;QAAWG,OAAO,EAAE,CAAC;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC5E;QAAWI,OAAO,EAAE,CAAC;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAC5E;QAAWK,MAAM,EAAE,CAAC;UAAEP,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAClE;IAEJ,CAAC;EACH,CAAC;EACAM,OAAOA,CAAA,EAAG;IACP,IAAIC,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;IACrD,IAAI,CAACpB,EAAC,GAAIgB,IAAI,CAACK,GAAG;IAClB,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EACHC,OAAO,EAAE;IAEb;IACQD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAI9B,IAAG,GAAI,wBAAuB,GAAI,IAAI,CAACI,EAAE;MACnDL,OAAO,CAACgC,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACzB,QAAO,GAAIa,IAAI,CAACC,KAAK,CAACD,IAAI,CAACa,SAAS,CAACD,GAAG,CAACE,OAAO,CAAC,CAAC;QACvD,IAAI,CAACN,WAAU,GAAI,KAAK;MAG1B,CAAC,CAAC;IACJ,CAAC;IAED;IACAO,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIT,GAAE,GAAI9B,IAAG,GAAI,uBAAuB;UACxC,IAAI,CAACO,UAAS,GAAI,IAAI;UAEtBR,OAAO,CAACgC,IAAI,CAACD,GAAG,EAAE,IAAI,CAACtB,QAAQ,CAAC,CAACwB,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACO,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZ7B,OAAO,EAAE,MAAM;gBACf8B,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACF,QAAQ,CAAC;gBACZ7B,OAAO,EAAEqB,GAAG,CAACW,GAAG;gBAChBF,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAACpC,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ;EAMF;AACN"}]}