package com.service;
import java.util.List;

import com.model.Propertystaff;
import com.util.PageBean;

public interface PropertystaffService{
	
	//查询多条记录
	public List<Propertystaff> queryPropertystaffList(Propertystaff propertystaff,PageBean page) throws Exception;
 
	//添加
	public int insertPropertystaff(Propertystaff propertystaff) throws Exception ;
	
	//根据ID删除
	public int deletePropertystaff(int id) throws Exception ;
	
	//更新
	public int updatePropertystaff(Propertystaff propertystaff) throws Exception ;
	
	//根据ID查询单条数据
	public Propertystaff queryPropertystaffById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Propertystaff propertystaff);

}

