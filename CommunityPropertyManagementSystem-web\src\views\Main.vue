﻿<template>
  <el-config-provider :locale="locale">
    <div class="wrapper" style="overflow-x: hidden;">
      <LeftMenu />

      <div id="content">
        <Header class="header-fixed" />

        <div class="main-content">
          <div class="container-fluid p-4">
            <div class="page-container">
              <div class="content-container">
                <div class="page-header d-flex justify-content-between align-items-center">
                  <h5 class="page-title">{{ this.$route.meta.title }}</h5>
                </div>

                <div class="table-responsive"><router-view /></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-config-provider>
</template>

<script>
import Header from '../components/Header';
import LeftMenu from '../components/LeftMenu';
import { ElConfigProvider } from 'element-plus';
import zhCn from 'element-plus/lib/locale/lang/zh-cn';
import $ from 'jquery';
export default {
  name: 'MainLayout',
  components: {
    Header,
    LeftMenu,
    [ElConfigProvider.name]: ElConfigProvider,
  },
  data() {
    return {
      locale: zhCn,
    };
  },
  mounted() {
  },

  methods: {},
};
</script>

<style scoped>
@import url(../assets/css/ht_bootstrap.min.css);
@import url(../assets/css/ht_bootstrap-icons.css);
@import url(../assets/css/ht_style.css);

.wrapper {
  display: flex;
  min-height: 100vh;
  position: relative;
}

#content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

.header-fixed {
  position: fixed;
  top: 0;
  right: 0;
  left: var(--sidebar-width);
  z-index: 100;
}

.main-content {
  flex: 1;
  padding-top: var(--header-height);
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

.content-container {
  position: relative;
  z-index: 2;
}

.table-responsive {
  position: relative;
  z-index: 3;
}

.page-header {
  margin-bottom: 1.5rem;
  padding: 1rem 0;
  border-bottom: 2px solid #eaecf4;
  background: linear-gradient(to right, #ffffff, #f8f9fc);
}

.page-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  padding: 0.5rem 1rem;
  position: relative;
}

.page-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--primary-color);
  border-radius: 2px;
}
</style>

