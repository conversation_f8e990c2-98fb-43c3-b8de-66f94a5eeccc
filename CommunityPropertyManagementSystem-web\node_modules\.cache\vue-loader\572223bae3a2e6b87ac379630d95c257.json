{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffAdd.vue", "mtime": 1749135166881}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\propertystaff\\PropertystaffAdd.vue"], "names": [], "mappings": ";AA2CA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEZ,CAAC;IACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACzE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3E,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACzE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1E,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACvE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC7E,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC7E,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5E,CAAC,SAAS,CAAC;;MAEL,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEV,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACV,CAAC,EAAE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;WACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACT,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;aACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;eACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;iBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACb,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC;eACH,EAAE,CAAC,CAAC,CAAC,EAAE;iBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACb,CAAC,CAAC;eACJ;eACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACvB,CAAC,CAAC;WACJ;;SAEF,CAAC,CAAC;IACP,CAAC;;OAEE,CAAC,EAAE,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC;QACJ,CAAC;;;;;MAKH,CAAC;AACP", "file": "I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/views/admin/propertystaff/PropertystaffAdd.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"用户名\" prop=\"plname\">\r\n<el-input v-model=\"formData.plname\" placeholder=\"用户名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"登录密码\" prop=\"pword\">\r\n<el-input v-model=\"formData.pword\" placeholder=\"登录密码\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"姓名\" prop=\"pname\">\r\n<el-input v-model=\"formData.pname\" placeholder=\"姓名\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"性别\" prop=\"gender\">\r\n<el-radio-group v-model=\"formData.gender\">\r\n<el-radio label=\"男\">\r\n男\r\n</el-radio>\r\n<el-radio label=\"女\">\r\n女\r\n</el-radio>\r\n</el-radio-group>\r\n</el-form-item>\r\n<el-form-item label=\"年龄\" prop=\"age\">\r\n<el-input v-model=\"formData.age\" placeholder=\"年龄\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系方式\" prop=\"contact\">\r\n<el-input v-model=\"formData.contact\" placeholder=\"联系方式\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"联系地址\" prop=\"address\">\r\n<el-input v-model=\"formData.address\" placeholder=\"联系地址\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'PropertystaffAdd',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {   \r\n        uploadVisible: false, \r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {}, //表单数据           \r\n        addrules: {\r\n          plname: [{ required: true, message: '请输入用户名', trigger: 'blur' },\r\n],          pword: [{ required: true, message: '请输入登录密码', trigger: 'blur' },\r\n],          pname: [{ required: true, message: '请输入姓名', trigger: 'blur' },\r\n],          gender: [{ required: true, message: '请输入性别', trigger: 'blur' },\r\n],          age: [{ required: true, message: '请输入年龄', trigger: 'blur' },\r\n],          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },\r\n],          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },\r\n],          status: [{ required: true, message: '请输入审核状态', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    mounted() {\r\n    \r\n    },\r\n\r\n \r\n    methods: {    \r\n   // 添加\r\n    save() {       \r\n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n           if (valid) {\r\n             let url = base + \"/propertystaff/add\";\r\n             this.btnLoading = true;\r\n             this.formData.status = '待审核';\r\n             request.post(url, this.formData).then((res) => { //发送请求         \r\n               if (res.code == 200) {\r\n                 this.$message({\r\n                   message: \"操作成功\",\r\n                   type: \"success\",\r\n                   offset: 320,\r\n                 });              \r\n                this.$router.push({\r\n                path: \"/PropertystaffManage\",\r\n                });\r\n               } else {\r\n                 this.$message({\r\n                   message: res.msg,\r\n                   type: \"error\",\r\n                   offset: 320,\r\n                 });\r\n               }\r\n               this.btnLoading=false;\r\n             });\r\n           }        \r\n           \r\n         });\r\n    },\r\n    \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/PropertystaffManage\",\r\n          });\r\n        },       \r\n              \r\n          \r\n           \r\n           \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}