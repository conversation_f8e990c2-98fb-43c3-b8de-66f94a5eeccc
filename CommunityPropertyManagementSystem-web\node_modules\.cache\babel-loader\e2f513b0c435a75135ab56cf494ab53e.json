{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\users\\UsersManage2.vue?vue&type=template&id=1e182fe6", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\users\\UsersManage2.vue", "mtime": 1749135366876}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_col", "span", "_component_el_form", "inline", "model", "$data", "filters", "_component_el_form_item", "_component_el_input", "ulname", "$event", "placeholder", "size", "uname", "contact", "_component_el_button", "type", "onClick", "$options", "query", "icon", "_component_el_table", "data", "datalist", "border", "stripe", "_component_el_table_column", "prop", "label", "align", "default", "_withCtx", "scope", "handleShow", "$index", "row", "_ctx", "handleApprove", "handleReject", "handleDelete", "listLoading", "_component_el_pagination", "onCurrentChange", "handleCurrentChange", "page", "currentPage", "pageSize", "background", "layout", "total", "totalCount"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\users\\UsersManage2.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\n<el-form :inline=\"true\" :model=\"filters\" >\n<el-form-item>\n<el-input v-model=\"filters.ulname\" placeholder=\"用户名\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-input v-model=\"filters.uname\" placeholder=\"姓名\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-input v-model=\"filters.contact\" placeholder=\"联系方式\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n</el-form-item>\n </el-form>\n</el-col>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"ulname\" label=\"用户名\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"loginpassword\" label=\"登录密码\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"uname\" label=\"姓名\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"gender\" label=\"性别\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"contact\" label=\"联系方式\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"registertime\" label=\"注册时间\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"uflag\" label=\"审核状态\"  align=\"center\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"300\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"success\" size=\"mini\" @click=\"handleApprove(scope.$index, scope.row)\" icon=\"el-icon-check\" style=\" padding: 3px 6px 3px 6px;\">通过</el-button>\n<el-button type=\"warning\" size=\"mini\" @click=\"handleReject(scope.$index, scope.row)\" icon=\"el-icon-close\" style=\" padding: 3px 6px 3px 6px;\">不通过</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" \n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'users',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          ulname: '',\n          uname: '',\n          contact: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        \n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据  \n    \n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n \n    methods: {    \n\n              \n       // 删除用户\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/users/del?id=\" + row.uid;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n        getDatas() {      \n          let para = {\n               ulname:this.filters.ulname,\n   uname:this.filters.uname,\n            contact: this.filters.contact,\n   uflag:'待审核'\n\n          };\n          this.listLoading = true;\n          let url = base + \"/users/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \n          request.post(url, para).then((res) => {   \n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },    \n                 //查询\n        query() {\n          this.getDatas();\n        },  \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/UsersDetail\",\n             query: {\n                id: row.uid,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/UsersEdit\",\n             query: {\n                id: row.uid,\n              },\n          });\n        },\n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;iDAaY,IAAE;iDAe+D,IAAE;iDACD,IAAE;iDACH,KAAG;iDACH,IAAE;;;;;;;;;;;uBA/B3IC,mBAAA,CAuCM,OAvCNC,UAuCM,GAtCJC,YAAA,CAeGC,iBAAA;IAfOC,IAAI,EAAE,EAAE;IAAGL,KAA8C,EAA9C;MAAA;MAAA;IAAA;;sBAC3B,MAaW,CAbXG,YAAA,CAaWG,kBAAA;MAbDC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,KAAA,CAAAC;;wBAChC,MAEe,CAFfP,YAAA,CAEeQ,uBAAA;0BADf,MAA8E,CAA9ER,YAAA,CAA8ES,mBAAA;sBAA3DH,KAAA,CAAAC,OAAO,CAACG,MAAM;qEAAdJ,KAAA,CAAAC,OAAO,CAACG,MAAM,GAAAC,MAAA;UAAEC,WAAW,EAAC,KAAK;UAAEC,IAAI,EAAC;;;UAE3Db,YAAA,CAEeQ,uBAAA;0BADf,MAA4E,CAA5ER,YAAA,CAA4ES,mBAAA;sBAAzDH,KAAA,CAAAC,OAAO,CAACO,KAAK;qEAAbR,KAAA,CAAAC,OAAO,CAACO,KAAK,GAAAH,MAAA;UAAEC,WAAW,EAAC,IAAI;UAAEC,IAAI,EAAC;;;UAEzDb,YAAA,CAEeQ,uBAAA;0BADf,MAAgF,CAAhFR,YAAA,CAAgFS,mBAAA;sBAA7DH,KAAA,CAAAC,OAAO,CAACQ,OAAO;qEAAfT,KAAA,CAAAC,OAAO,CAACQ,OAAO,GAAAJ,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAEC,IAAI,EAAC;;;UAE7Db,YAAA,CAEeQ,uBAAA;0BADf,MAA0F,CAA1FR,YAAA,CAA0FgB,oBAAA;UAA/EC,IAAI,EAAC,SAAS;UAACJ,IAAI,EAAC,OAAO;UAAEK,OAAK,EAAEC,QAAA,CAAAC,KAAK;UAAEC,IAAI,EAAC;;4BAAiB,MAAE,C;;;;;;;;;sBAK9ErB,YAAA,CAgBWsB,mBAAA;IAhBAC,IAAI,EAAEjB,KAAA,CAAAkB,QAAQ;IAAEC,MAAM,EAAN,EAAM;IAACC,MAAM,EAAN,EAAM;IAAC7B,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAA4B,uBAAqB,EAArB,EAAqB;IAAG,YAAU,EAAC,KAAK;IAAKgB,IAAI,EAAC;;sBAC1I,MAA6E,CAA7Eb,YAAA,CAA6E2B,0BAAA;MAA5DC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAC,KAAK;MAAEC,KAAK,EAAC;QAClD9B,YAAA,CAAqF2B,0BAAA;MAApEC,IAAI,EAAC,eAAe;MAACC,KAAK,EAAC,MAAM;MAAEC,KAAK,EAAC;QAC1D9B,YAAA,CAA2E2B,0BAAA;MAA1DC,IAAI,EAAC,OAAO;MAACC,KAAK,EAAC,IAAI;MAAEC,KAAK,EAAC;QAChD9B,YAAA,CAA4E2B,0BAAA;MAA3DC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAC,IAAI;MAAEC,KAAK,EAAC;QACjD9B,YAAA,CAA+E2B,0BAAA;MAA9DC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAC,MAAM;MAAEC,KAAK,EAAC;QACpD9B,YAAA,CAAoF2B,0BAAA;MAAnEC,IAAI,EAAC,cAAc;MAACC,KAAK,EAAC,MAAM;MAAEC,KAAK,EAAC;QACzD9B,YAAA,CAA6E2B,0BAAA;MAA5DC,IAAI,EAAC,OAAO;MAACC,KAAK,EAAC,MAAM;MAAEC,KAAK,EAAC;QAClD9B,YAAA,CAOkB2B,0BAAA;MAPDE,KAAK,EAAC,IAAI;MAAC,WAAS,EAAC,KAAK;MAACC,KAAK,EAAC;;MACvCC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACzBjC,YAAA,CAA2JgB,oBAAA;QAAhJC,IAAI,EAAC,SAAS;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAAe,UAAU,CAACD,KAAK,CAACE,MAAM,EAAEF,KAAK,CAACG,GAAG;QAAGf,IAAI,EAAC,iBAAiB;QAACxB,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAE,C;;wDAC/IG,YAAA,CAA4JgB,oBAAA;QAAjJC,IAAI,EAAC,SAAS;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAE0B,IAAA,CAAAC,aAAa,CAACL,KAAK,CAACE,MAAM,EAAEF,KAAK,CAACG,GAAG;QAAGf,IAAI,EAAC,eAAe;QAACxB,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAE,C;;wDAChJG,YAAA,CAA4JgB,oBAAA;QAAjJC,IAAI,EAAC,SAAS;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAE0B,IAAA,CAAAE,YAAY,CAACN,KAAK,CAACE,MAAM,EAAEF,KAAK,CAACG,GAAG;QAAGf,IAAI,EAAC,eAAe;QAACxB,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAG,C;;wDAChJG,YAAA,CAA2JgB,oBAAA;QAAhJC,IAAI,EAAC,QAAQ;QAACJ,IAAI,EAAC,MAAM;QAAEK,OAAK,EAAAP,MAAA,IAAEQ,QAAA,CAAAqB,YAAY,CAACP,KAAK,CAACE,MAAM,EAAEF,KAAK,CAACG,GAAG;QAAGf,IAAI,EAAC,gBAAgB;QAACxB,KAAkC,EAAlC;UAAA;QAAA;;0BAAmC,MAAE,C;;;;;;;qDAbtES,KAAA,CAAAmC,WAAW,E,GAiBpFzC,YAAA,CAE6D0C,wBAAA;IAF5CC,eAAc,EAAExB,QAAA,CAAAyB,mBAAmB;IAAG,cAAY,EAAEtC,KAAA,CAAAuC,IAAI,CAACC,WAAW;IAAG,WAAS,EAAExC,KAAA,CAAAuC,IAAI,CAACE,QAAQ;IAC/GC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAC,kCAAkC;IAAEC,KAAK,EAAE5C,KAAA,CAAAuC,IAAI,CAACM,UAAU;IAC5EtD,KAA2C,EAA3C;MAAA;MAAA;IAAA"}]}