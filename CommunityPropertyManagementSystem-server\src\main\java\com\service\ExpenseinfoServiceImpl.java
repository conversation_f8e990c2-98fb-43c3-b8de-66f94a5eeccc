package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.ExpenseinfoMapper;
import com.model.Expenseinfo;
import com.util.PageBean;
@Service
public class ExpenseinfoServiceImpl implements ExpenseinfoService{
        
	@Autowired
	private ExpenseinfoMapper expenseinfoMapper;

	//查询多条记录
	public List<Expenseinfo> queryExpenseinfoList(Expenseinfo expenseinfo,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(expenseinfo, page);
		
		List<Expenseinfo> getExpenseinfo = expenseinfoMapper.query(map);
		
		return getExpenseinfo;
	}
	
	//得到记录总数
	@Override
	public int getCount(Expenseinfo expenseinfo) {
		Map<String, Object> map = getQueryMap(expenseinfo, null);
		int count = expenseinfoMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Expenseinfo expenseinfo,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(expenseinfo!=null){
			map.put("eid", expenseinfo.getEid());
			map.put("etitle", expenseinfo.getEtitle());
			map.put("hno", expenseinfo.getHno());
			map.put("eamount", expenseinfo.getEamount());
			map.put("edescription", expenseinfo.getEdescription());
			map.put("etime", expenseinfo.getEtime());
			map.put("epaymentstatus", expenseinfo.getEpaymentstatus());
			map.put("sort", expenseinfo.getSort());
			map.put("condition", expenseinfo.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertExpenseinfo(Expenseinfo expenseinfo) throws Exception {
		return expenseinfoMapper.insertExpenseinfo(expenseinfo);
	}

	//根据ID删除
	public int deleteExpenseinfo(int id) throws Exception {
		return expenseinfoMapper.deleteExpenseinfo(id);
	}

	//更新
	public int updateExpenseinfo(Expenseinfo expenseinfo) throws Exception {
		return expenseinfoMapper.updateExpenseinfo(expenseinfo);
	}
	
	//根据ID得到对应的记录
	public Expenseinfo queryExpenseinfoById(int id) throws Exception {
		Expenseinfo po =  expenseinfoMapper.queryExpenseinfoById(id);
		return po;
	}
}

