package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.HouseMapper;
import com.model.House;
import com.util.PageBean;
@Service
public class HouseServiceImpl implements HouseService{
        
	@Autowired
	private HouseMapper houseMapper;

	//查询多条记录
	public List<House> queryHouseList(House house,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(house, page);
		
		List<House> getHouse = houseMapper.query(map);
		
		return getHouse;
	}
	
	//得到记录总数
	@Override
	public int getCount(House house) {
		Map<String, Object> map = getQueryMap(house, null);
		int count = houseMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(House house,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(house!=null){
			map.put("hid", house.getHid());
			map.put("hno", house.getHno());
			map.put("buildinfo", house.getBuildinfo());
			map.put("htype", house.getHtype());
			map.put("areasinfo", house.getAreasinfo());
			map.put("hord", house.getHord());
			map.put("uid", house.getUid());
			map.put("sort", house.getSort());
			map.put("condition", house.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertHouse(House house) throws Exception {
		return houseMapper.insertHouse(house);
	}

	//根据ID删除
	public int deleteHouse(int id) throws Exception {
		return houseMapper.deleteHouse(id);
	}

	//更新
	public int updateHouse(House house) throws Exception {
		return houseMapper.updateHouse(house);
	}
	
	//根据ID得到对应的记录
	public House queryHouseById(int id) throws Exception {
		House po =  houseMapper.queryHouseById(id);
		return po;
	}
}

