﻿<template>
  <div class="login-page">
    <div class="container">
      <div class="login-container">
        <div class="login-box">
          <div class="text-center mb-4">
            <i class="bi bi-shield-lock text-primary" style="font-size: 3rem"></i>
            <h2 class="mt-3">小区物业管理系统</h2>
            <p class="text-muted">请输入您的账号和密码</p>
          </div>
          <form id="loginForm">
            <div class="form-floating mb-4">
              <input type="text" class="form-control" id="username" placeholder="用户名" v-model="loginModel.username" />
              <label for="username"><i class="bi bi-person me-2"></i>用户名</label>
            </div>
            <div class="form-floating mb-4">
              <input type="password" class="form-control" id="password" placeholder="密码"
                v-model="loginModel.password" />
              <label for="password"><i class="bi bi-key me-2"></i>密码</label>
            </div>
            <div class="mb-4">
              <div class="role-selection">
                <el-radio label="管理员" v-model="loginModel.radio">管理员</el-radio>
                <el-radio label="用户" v-model="loginModel.radio">用户</el-radio>
                <el-radio label="工作人员" v-model="loginModel.radio">工作人员</el-radio>

              </div>
            </div>

            <button type="button" class="btn btn-primary w-100 py-2 mb-3" @click="login">
              <i class="bi bi-box-arrow-in-right me-2"></i>登录
            </button>
            <div style="text-align: center;">
              <a href="#" @click="toreg"> 用户注册 </a>&nbsp;&nbsp;|&nbsp;&nbsp;


              <a href="#" @click="toreg2"> 工作人员注册 </a>
            </div>



          </form>

        </div>
        <div class="text-center text-white mt-4">
          <small>&copy; 管理系统. All rights reserved.</small>
        </div>
      </div>
    </div>
  </div>
  <el-dialog title="用户注册" v-model="userRegVisible" width="40%" :close-on-click-modal="false">
    <el-form :model="userFormData" label-width="20%" ref="userFormDataRef" :rules="userRules" align="left">
      <el-form-item label="用户名" prop="ulname">
        <el-input v-model="userFormData.ulname" placeholder="用户名" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="登录密码" prop="loginpassword">
        <el-input type="password" v-model="userFormData.loginpassword" placeholder="登录密码" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="loginpassword2">
        <el-input type="password" v-model="userFormData.loginpassword2" placeholder="确认密码"
          style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="姓名" prop="uname">
        <el-input v-model="userFormData.uname" placeholder="姓名" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="userFormData.gender">
          <el-radio label="男">
            男
          </el-radio>
          <el-radio label="女">
            女
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input v-model="userFormData.contact" placeholder="联系方式" style="width:50%;"></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="userReg" :loading="btnLoading">注 册</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>

  <el-dialog title="工作人员注册" v-model="staffRegVisible" width="40%" :close-on-click-modal="false">
    <el-form :model="staffFormData" label-width="20%" ref="staffFormDataRef" :rules="staffRules" align="left">
      <el-form-item label="用户名" prop="plname">
        <el-input v-model="staffFormData.plname" placeholder="用户名" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="登录密码" prop="pword">
        <el-input type="password" v-model="staffFormData.pword" placeholder="登录密码" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="pword2">
        <el-input type="password" v-model="staffFormData.pword2" placeholder="确认密码" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="姓名" prop="pname">
        <el-input v-model="staffFormData.pname" placeholder="姓名" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="staffFormData.gender">
          <el-radio label="男">
            男
          </el-radio>
          <el-radio label="女">
            女
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input v-model="staffFormData.age" placeholder="年龄" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input v-model="staffFormData.contact" placeholder="联系方式" style="width:50%;"></el-input>
      </el-form-item>
      <el-form-item label="联系地址" prop="address">
        <el-input v-model="staffFormData.address" placeholder="联系地址" style="width:50%;"></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="staffReg" :loading="btnLoading">注 册</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
import request, { base } from "../../utils/http";
export default {
  name: "Login",
  data() {
    return {
      year: new Date().getFullYear(),
      loginModel: {
        username: "",
        password: "",
        radio: "管理员",
      },
      loginModel2: {},

      // 用户注册相关
      userRegVisible: false,
      userFormData: {},
      userRules: {
        ulname: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        loginpassword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
        loginpassword2: [
          { required: true, message: '请输入确认密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.userFormData.loginpassword) {
                callback(new Error('两次输入密码不一致!'));
              } else {
                callback();
              }
            }, trigger: 'blur'
          }
        ],
        uname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        gender: [{ required: true, message: '请选择性别', trigger: 'blur' }],
        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }]
      },

      // 工作人员注册相关
      staffRegVisible: false,
      staffFormData: {},
      staffRules: {
        plname: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        pword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
        pword2: [
          { required: true, message: '请输入确认密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.staffFormData.pword) {
                callback(new Error('两次输入密码不一致!'));
              } else {
                callback();
              }
            }, trigger: 'blur'
          }
        ],
        pname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        gender: [{ required: true, message: '请选择性别', trigger: 'blur' }],
        age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' }]
      },

      btnLoading: false, //按钮是否在加载中
    };
  },
  mounted() { },
  created() {

  },
  methods: {
    login() {
      let that = this;

      if (that.loginModel.username == "") {
        that.$message({
          message: "请输入账号",
          type: "warning",
        });
        return;
      }
      if (that.loginModel.password == "") {
        that.$message({
          message: "请输入密码",
          type: "warning",
        });
        return;
      }

      this.loading = true;
      var role = that.loginModel.radio; //获取身份
      if (role == '管理员') {
        let url = base + "/admin/login";
        this.loginModel2.aname = this.loginModel.username;
        this.loginModel2.password = this.loginModel.password;
        request.post(url, this.loginModel2).then((res) => {
          this.loading = false;
          if (res.code == 200) {
            console.log(JSON.stringify(res.resdata));
            sessionStorage.setItem("user", JSON.stringify(res.resdata));
            sessionStorage.setItem("userLname", res.resdata.aname);
            sessionStorage.setItem("role", "管理员");
            this.$router.push("/main");
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      }
      else if (role == '工作人员') {
        let url = base + "/propertystaff/login";
        this.loginModel2.plname = this.loginModel.username;
        this.loginModel2.pword = this.loginModel.password;
        request.post(url, this.loginModel2).then((res) => {
          this.loading = false;
          if (res.code == 200) {
            console.log(JSON.stringify(res.resdata));
            sessionStorage.setItem("user", JSON.stringify(res.resdata));
            sessionStorage.setItem("userLname", res.resdata.plname);
            sessionStorage.setItem("role", "工作人员");
            this.$router.push("/main");
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      }
      else if (role == '用户') {
        let url = base + "/users/login";
        this.loginModel2.ulname = this.loginModel.username;
        this.loginModel2.loginpassword = this.loginModel.password;
        request.post(url, this.loginModel2).then((res) => {
          this.loading = false;
          if (res.code == 200) {
            console.log(JSON.stringify(res.resdata));
            sessionStorage.setItem("user", JSON.stringify(res.resdata));
            sessionStorage.setItem("userLname", res.resdata.ulname);
            sessionStorage.setItem("role", "用户");
            this.$router.push("/main");
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        });
      }


    },

    // 打开用户注册窗口
    toreg() {
      this.userRegVisible = true;
      this.userFormData = {};
      this.$nextTick(() => {
        this.$refs["userFormDataRef"].resetFields();
      });
    },

    // 打开工作人员注册窗口
    toreg2() {
      this.staffRegVisible = true;
      this.staffFormData = {};
      this.$nextTick(() => {
        this.$refs["staffFormDataRef"].resetFields();
      });
    },

    // 用户注册
    userReg() {
      this.$refs["userFormDataRef"].validate((valid) => {
        if (valid) {
          let url = base + "/users/add";
          this.btnLoading = true;
          this.userFormData.uflag = "待审核";
          request.post(url, this.userFormData).then((res) => {
            this.btnLoading = false;
            if (res.code == 200) {
              this.$message({
                message: "恭喜您，注册成功，请等待管理员的审核！",
                type: "success",
                offset: 320,
              });
              this.userRegVisible = false;
              this.$refs["userFormDataRef"].resetFields();
              this.$refs["userFormDataRef"].clearValidate();
            } else if (res.code == 201) {
              this.$message({
                message: res.msg,
                type: "error",
                offset: 320,
              });
            } else {
              this.$message({
                message: "服务器错误",
                type: "error",
                offset: 320,
              });
            }
          });
        }
      });
    },

    // 工作人员注册
    staffReg() {
      this.$refs["staffFormDataRef"].validate((valid) => {
        if (valid) {
          let url = base + "/propertystaff/add";
          this.btnLoading = true;
          this.staffFormData.status = "待审核";
          request.post(url, this.staffFormData).then((res) => {
            this.btnLoading = false;
            if (res.code == 200) {
              this.$message({
                message: "恭喜您，注册成功，请等待管理员的审核！",
                type: "success",
                offset: 320,
              });
              this.staffRegVisible = false;
              this.$refs["staffFormDataRef"].resetFields();
              this.$refs["staffFormDataRef"].clearValidate();
            } else if (res.code == 201) {
              this.$message({
                message: res.msg,
                type: "error",
                offset: 320,
              });
            } else {
              this.$message({
                message: "服务器错误",
                type: "error",
                offset: 320,
              });
            }
          });
        }
      });
    },





  },
};
</script>

<style lang="scss" scoped>
@import url(../assets/css/ht_bootstrap.min.css);
@import url(../assets/css/ht_bootstrap-icons.css);
@import url(../assets/css/ht_style.css);

.role-selection {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding: 10px 0;
}

.role-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.role-option input[type='radio'] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.role-option label {
  margin: 0;
  cursor: pointer;
  font-size: 1rem;
  color: #6e707e;
}

.role-option:hover label {
  color: var(--primary-color);
}
</style>
