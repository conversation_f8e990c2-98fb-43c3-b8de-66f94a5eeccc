<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.UsersMapper">
	<select id="findUsersList"  resultType="Users">
		select * from users 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Users">
	    select  *  
        from users a  	
		<where>
      		<if test="uid != null and uid !=0 ">
		    and a.uid = #{uid}
		</if>
		<if test="ulname != null and ulname != ''">
		    and a.ulname = #{ulname}
		</if>
		<if test="loginpassword != null and loginpassword != ''">
		    and a.loginpassword = #{loginpassword}
		</if>
		<if test="uname != null and uname != ''">
		    and a.uname = #{uname}
		</if>
		<if test="gender != null and gender != ''">
		    and a.gender = #{gender}
		</if>
		<if test="contact != null and contact != ''">
		    and a.contact = #{contact}
		</if>
		<if test="registertime != null and registertime != ''">
		    and a.registertime = #{registertime}
		</if>
		<if test="uflag != null and uflag != ''">
		    and a.uflag = #{uflag}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} uid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from users a  
		<where>
      		<if test="uid != null and uid !=0 ">
		    and a.uid = #{uid}
		</if>
		<if test="ulname != null and ulname != ''">
		    and a.ulname = #{ulname}
		</if>
		<if test="loginpassword != null and loginpassword != ''">
		    and a.loginpassword = #{loginpassword}
		</if>
		<if test="uname != null and uname != ''">
		    and a.uname = #{uname}
		</if>
		<if test="gender != null and gender != ''">
		    and a.gender = #{gender}
		</if>
		<if test="contact != null and contact != ''">
		    and a.contact = #{contact}
		</if>
		<if test="registertime != null and registertime != ''">
		    and a.registertime = #{registertime}
		</if>
		<if test="uflag != null and uflag != ''">
		    and a.uflag = #{uflag}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryUsersById" parameterType="int" resultType="Users">
    select  *  
     from users a  	 where a.uid=#{value}
  </select>
 
	<insert id="insertUsers" useGeneratedKeys="true" keyProperty="uid" parameterType="Users">
    insert into users
    (ulname,loginpassword,uname,gender,contact,registertime,uflag)
    values
    (#{ulname},#{loginpassword},#{uname},#{gender},#{contact},now(),#{uflag});
  </insert>
	
	<update id="updateUsers" parameterType="Users" >
    update users 
    <set>
		<if test="ulname != null and ulname != ''">
		    ulname = #{ulname},
		</if>
		<if test="loginpassword != null and loginpassword != ''">
		    loginpassword = #{loginpassword},
		</if>
		<if test="uname != null and uname != ''">
		    uname = #{uname},
		</if>
		<if test="gender != null and gender != ''">
		    gender = #{gender},
		</if>
		<if test="contact != null and contact != ''">
		    contact = #{contact},
		</if>
		<if test="registertime != null and registertime != ''">
		    registertime = #{registertime},
		</if>
		<if test="uflag != null and uflag != ''">
		    uflag = #{uflag},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="uid != null or uid != ''">
      uid=#{uid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteUsers" parameterType="int">
    delete from  users where uid=#{value}
  </delete>

	
	
</mapper>

 
