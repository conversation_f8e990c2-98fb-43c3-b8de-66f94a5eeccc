package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/complaints")
public class ComplaintsController{
	
	@Resource
	private ComplaintsService complaintsService;
	
	//投诉建议列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Complaints>> list(@RequestBody Complaints complaints, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = complaintsService.getCount(complaints);
		//获取当前页记录
		List<Complaints> complaintsList = complaintsService.queryComplaintsList(complaints, page);
		//遍历
		for (Complaints complaints2 : complaintsList) {
			complaints2.setCcontent(removeHTML.Html2Text(complaints2.getCcontent()));

		}

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(complaintsList, counts, page_count);
	}
        
	//添加投诉建议
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Complaints complaints, HttpServletRequest req) throws Exception {
		try {
			complaintsService.insertComplaints(complaints); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除投诉建议
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			complaintsService.deleteComplaints(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改投诉建议
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Complaints complaints, HttpServletRequest req) throws Exception {
		try {
			complaintsService.updateComplaints(complaints); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回投诉建议详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Complaints complaints=complaintsService.queryComplaintsById(id); //根据ID查询
			return Response.success(complaints);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

