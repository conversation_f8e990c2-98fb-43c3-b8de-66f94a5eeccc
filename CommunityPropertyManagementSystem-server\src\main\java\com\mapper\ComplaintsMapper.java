package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Complaints;

public interface ComplaintsMapper {

	//返回所有记录
	public List<Complaints> findComplaintsList();
	
	//查询多条记录
	public List<Complaints> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertComplaints(Complaints complaints);

	//根据ID删除
	public int deleteComplaints(int id);
	
	//更新
	public int updateComplaints(Complaints complaints);
	
	//根据ID得到对应的记录
	public Complaints queryComplaintsById(int id);
	
}

