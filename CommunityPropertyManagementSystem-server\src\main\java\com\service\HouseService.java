package com.service;
import java.util.List;

import com.model.House;
import com.util.PageBean;

public interface HouseService{
	
	//查询多条记录
	public List<House> queryHouseList(House house,PageBean page) throws Exception;
 
	//添加
	public int insertHouse(House house) throws Exception ;
	
	//根据ID删除
	public int deleteHouse(int id) throws Exception ;
	
	//更新
	public int updateHouse(House house) throws Exception ;
	
	//根据ID查询单条数据
	public House queryHouseById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(House house);

}

