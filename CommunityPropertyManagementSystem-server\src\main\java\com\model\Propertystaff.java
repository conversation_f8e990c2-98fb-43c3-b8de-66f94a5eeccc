package com.model;
import java.util.List;

/**
* (propertystaff)工作人员实体类
*/
public class Propertystaff extends ComData{
	
	private static final long serialVersionUID = 587221482216851L;
	private Integer pid;    //人员id
	private String plname;    //用户名
	private String pword;    //登录密码
	private String pname;    //姓名
	private String gender;    //性别
	private Integer age;    //年龄
	private String contact;    //联系方式
	private String address;    //联系地址
	private String regtime;    //注册时间
	private String status;    //审核状态

	public Integer getPid() {
		return pid;
	}

	public void setPid(Integer pid) {
		this.pid = pid;
	}

	public String getPlname() {
		return plname;
	}

	public void setPlname(String plname) {
		this.plname = plname;
	}

	public String getPword() {
		return pword;
	}

	public void setPword(String pword) {
		this.pword = pword;
	}

	public String getPname() {
		return pname;
	}

	public void setPname(String pname) {
		this.pname = pname;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getRegtime() {
		return regtime;
	}

	public void setRegtime(String regtime) {
		this.regtime = regtime;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}

