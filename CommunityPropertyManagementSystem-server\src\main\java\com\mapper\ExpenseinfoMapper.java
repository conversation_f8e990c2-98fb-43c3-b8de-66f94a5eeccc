package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Expenseinfo;

public interface ExpenseinfoMapper {

	//返回所有记录
	public List<Expenseinfo> findExpenseinfoList();
	
	//查询多条记录
	public List<Expenseinfo> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertExpenseinfo(Expenseinfo expenseinfo);

	//根据ID删除
	public int deleteExpenseinfo(int id);
	
	//更新
	public int updateExpenseinfo(Expenseinfo expenseinfo);
	
	//根据ID得到对应的记录
	public Expenseinfo queryExpenseinfoById(int id);
	
}

