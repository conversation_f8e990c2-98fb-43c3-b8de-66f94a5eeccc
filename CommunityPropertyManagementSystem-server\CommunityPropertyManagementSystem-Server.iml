<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5Db21tdW5pdHlQcm9wZXJ0eU1hbmFnZW1lbnRTeXN0ZW0tU2VydmVyPC9pZD48Y2xhc3NwYXRoPjxkaXIgbmFtZT0iSTovcHJvZHVjdDQvMDA0NTNDb21tdW5pdHlQcm9wZXJ0eU1hbmFnZW1lbnRTeXN0ZW0vQ29tbXVuaXR5UHJvcGVydHlNYW5hZ2VtZW50U3lzdGVtLXNlcnZlci90YXJnZXQvY2xhc3NlcyI+PC9kaXI+PC9jbGFzc3BhdGg+PC9hcHBsaWNhdGlvbj4=" />
          </map>
        </option>
        <option name="version" value="5" />
      </configuration>
    </facet>
  </component>
</module>