<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="17" project-jdk-type="JavaSDK" />
  <component name="SaveActionSettings">
    <option name="actions">
      <set>
        <option value="activate" />
        <option value="activateOnShortcut" />
        <option value="organizeImports" />
        <option value="reformat" />
      </set>
    </option>
    <option name="configurationPath" value="" />
  </component>
</project>