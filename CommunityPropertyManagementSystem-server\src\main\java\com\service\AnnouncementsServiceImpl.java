package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.AnnouncementsMapper;
import com.model.Announcements;
import com.util.PageBean;
@Service
public class AnnouncementsServiceImpl implements AnnouncementsService{
        
	@Autowired
	private AnnouncementsMapper announcementsMapper;

	//查询多条记录
	public List<Announcements> queryAnnouncementsList(Announcements announcements,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(announcements, page);
		
		List<Announcements> getAnnouncements = announcementsMapper.query(map);
		
		return getAnnouncements;
	}
	
	//得到记录总数
	@Override
	public int getCount(Announcements announcements) {
		Map<String, Object> map = getQueryMap(announcements, null);
		int count = announcementsMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Announcements announcements,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(announcements!=null){
			map.put("aid", announcements.getAid());
			map.put("atitle", announcements.getAtitle());
			map.put("adetail", announcements.getAdetail());
			map.put("apubtime", announcements.getApubtime());
			map.put("sort", announcements.getSort());
			map.put("condition", announcements.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertAnnouncements(Announcements announcements) throws Exception {
		return announcementsMapper.insertAnnouncements(announcements);
	}

	//根据ID删除
	public int deleteAnnouncements(int id) throws Exception {
		return announcementsMapper.deleteAnnouncements(id);
	}

	//更新
	public int updateAnnouncements(Announcements announcements) throws Exception {
		return announcementsMapper.updateAnnouncements(announcements);
	}
	
	//根据ID得到对应的记录
	public Announcements queryAnnouncementsById(int id) throws Exception {
		Announcements po =  announcementsMapper.queryAnnouncementsById(id);
		return po;
	}
}

