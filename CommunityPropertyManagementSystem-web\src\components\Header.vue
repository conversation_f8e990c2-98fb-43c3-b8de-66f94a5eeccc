﻿<template>
  <nav class="navbar navbar-expand-lg navbar-light bg-white">
    <div class="container-fluid">
      <div class="ms-auto d-flex align-items-center">
        <div class="current-time me-4">
          <i class="bi bi-clock me-2"></i>
          <span id="currentTime"></span>
        </div>
        <el-dropdown trigger="click">
          <div class="el-dropdown-link d-flex align-items-center" style="cursor: pointer">
            <img
                src="../assets/images/touxiang.png"
                class="rounded-circle me-2"
                style="width: 32px; height: 32px; object-fit: cover"
            />
            <span>{{ role }}</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
            
              <el-dropdown-item>
                <router-link to="/password" class="dropdown-link">
                  <i class="bi bi-key me-2"></i>修改密码
                </router-link>
              </el-dropdown-item>
              <el-dropdown-item divided @click="exit">
                <i class="bi bi-box-arrow-right me-2"></i>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </nav>
</template>

<script>
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus';

export default {
  components: {
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
  },
  data() {
    return {
      userLname: '',
      role: '',
      currentTime: '',
      timer: null,
    };
  },
  mounted() {
    this.userLname = sessionStorage.getItem('userLname');
    this.role = sessionStorage.getItem('role');
    this.updateTime();
    this.timer = setInterval(this.updateTime, 1000);
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    updateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const date = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      const weekDay = weekDays[now.getDay()];

      this.currentTime = `${year}年${month}月${date}日 ${hours}:${minutes}:${seconds} ${weekDay}`;
      document.getElementById('currentTime').textContent = this.currentTime;
    },
    exit() {
      this.$confirm('确认退出吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
          .then(() => {
            sessionStorage.removeItem('userLname');
            sessionStorage.removeItem('role');
            this.$router.push('/');
          })
          .catch(() => {
          });
    },
  },
};
</script>

<style scoped>
.navbar {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1050;
}

.current-time {
  font-size: 0.95rem;
  color: #666;
  min-width: 300px;
  text-align: right;
}

.dropdown-link {
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  width: 100%;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  line-height: 1.5;
  min-width: 120px;
}

:deep(.el-dropdown-menu__item i) {
  margin-right: 8px;
}

:deep(.el-popper) {
  min-width: 150px;
}
</style>

