﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="门牌号" prop="hno">
<el-input v-model="formData.hno" placeholder="门牌号"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="所属楼栋" prop="buildinfo">
<el-input v-model="formData.buildinfo" placeholder="所属楼栋"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="户型" prop="htype">
<el-input v-model="formData.htype" placeholder="户型"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="面积/m2" prop="areasinfo">
<el-input v-model="formData.areasinfo" placeholder="面积/m2"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="朝向" prop="hord">
<el-input v-model="formData.hord" placeholder="朝向"  style="width:50%;" ></el-input>
</el-form-item>

<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'HouseEdit',
  components: {
    
  },  
    data() {
      return {   
        id: '',
        isClear: false,
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          hno: [{ required: true, message: '请输入门牌号', trigger: 'blur' },
],          buildinfo: [{ required: true, message: '请输入所属楼栋', trigger: 'blur' },
],          htype: [{ required: true, message: '请输入户型', trigger: 'blur' },
],          areasinfo: [{ required: true, message: '请输入面积/m2', trigger: 'blur' },
],          hord: [{ required: true, message: '请输入朝向', trigger: 'blur' },
],          uid: [{ required: true, message: '请输入用户id', trigger: 'blur' },
],        },

      };
    },
    created() {
    this.id = this.$route.query.id;
      this.getDatas();
    },

 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/house/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
            
            
          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/house/update";
              this.btnLoading = true;
              
              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });
                  this.$router.push({
                    path: "/HouseManage",
                  });
                } else {
                  this.$message({
                    message:res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
        
       // 返回
        goBack() {
          this.$router.push({
            path: "/HouseManage",
          });
        },       
              
          
           
           
      },
}

</script>
<style scoped>
</style>
 

