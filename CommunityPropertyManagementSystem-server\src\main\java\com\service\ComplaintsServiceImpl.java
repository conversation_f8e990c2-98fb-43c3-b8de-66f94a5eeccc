package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.ComplaintsMapper;
import com.model.Complaints;
import com.util.PageBean;
@Service
public class ComplaintsServiceImpl implements ComplaintsService{
        
	@Autowired
	private ComplaintsMapper complaintsMapper;

	//查询多条记录
	public List<Complaints> queryComplaintsList(Complaints complaints,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(complaints, page);
		
		List<Complaints> getComplaints = complaintsMapper.query(map);
		
		return getComplaints;
	}
	
	//得到记录总数
	@Override
	public int getCount(Complaints complaints) {
		Map<String, Object> map = getQueryMap(complaints, null);
		int count = complaintsMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Complaints complaints,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(complaints!=null){
			map.put("cid", complaints.getCid());
			map.put("ctype", complaints.getCtype());
			map.put("ctitle", complaints.getCtitle());
			map.put("ccontent", complaints.getCcontent());
			map.put("uid", complaints.getUid());
			map.put("ctime", complaints.getCtime());
			map.put("cstatus", complaints.getCstatus());
			map.put("cresult", complaints.getCresult());
			map.put("sort", complaints.getSort());
			map.put("condition", complaints.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertComplaints(Complaints complaints) throws Exception {
		return complaintsMapper.insertComplaints(complaints);
	}

	//根据ID删除
	public int deleteComplaints(int id) throws Exception {
		return complaintsMapper.deleteComplaints(id);
	}

	//更新
	public int updateComplaints(Complaints complaints) throws Exception {
		return complaintsMapper.updateComplaints(complaints);
	}
	
	//根据ID得到对应的记录
	public Complaints queryComplaintsById(int id) throws Exception {
		Complaints po =  complaintsMapper.queryComplaintsById(id);
		return po;
	}
}

