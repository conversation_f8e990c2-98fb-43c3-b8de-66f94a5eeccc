{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\users\\UsersManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\users\\UsersManage.vue", "mtime": 1749135110925}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "filters", "ulname", "uname", "contact", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "created", "getDatas", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "uid", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "para", "uflag", "resdata", "length", "isPage", "count", "query", "handleShow", "$router", "push", "path", "id", "handleEdit"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\users\\UsersManage.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <el-col  :span=\"24\"  style=\"padding-bottom: 0px; margin-left: 10px\">\n<el-form :inline=\"true\" :model=\"filters\" >\n<el-form-item>\n<el-input v-model=\"filters.ulname\" placeholder=\"用户名\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-input v-model=\"filters.uname\" placeholder=\"姓名\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-input v-model=\"filters.contact\" placeholder=\"联系方式\"  size=\"small\"></el-input>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"query\" icon=\"el-icon-search\">搜索</el-button>\n</el-form-item>\n </el-form>\n</el-col>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"ulname\" label=\"用户名\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"loginpassword\" label=\"登录密码\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"uname\" label=\"姓名\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"gender\" label=\"性别\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"contact\" label=\"联系方式\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"registertime\" label=\"注册时间\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"uflag\" label=\"审核状态\"  align=\"center\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleShow(scope.$index, scope.row)\" icon=\"el-icon-zoom-in\" style=\" padding: 3px 6px 3px 6px;\">详情</el-button>\n<el-button type=\"success\" size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\" icon=\"el-icon-edit\" style=\" padding: 3px 6px 3px 6px;\">编辑</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" \n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'users',\n  components: {\n    \n  },  \n    data() {\n      return {\n               filters: {\n          //列表查询参数\n          ulname: '',\n          uname: '',\n          contact: '',\n        },\n\n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        \n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据  \n    \n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n \n    methods: {    \n\n              \n       // 删除用户\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/users/del?id=\" + row.uid;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n        getDatas() {      \n          let para = {\n               ulname:this.filters.ulname,\n   uname:this.filters.uname,\n            contact: this.filters.contact,\n   uflag\n\n          };\n          this.listLoading = true;\n          let url = base + \"/users/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;        \n          request.post(url, para).then((res) => {   \n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          });\n        },    \n                 //查询\n        query() {\n          this.getDatas();\n        },  \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/UsersDetail\",\n             query: {\n                id: row.uid,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/UsersEdit\",\n             query: {\n                id: row.uid,\n              },\n          });\n        },\n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";AA2CA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MACEC,OAAO,EAAE;QACd;QACAC,MAAM,EAAE,EAAE;QACVC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE;MACX,CAAC;MAEDC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAEhB,CAAC;EACH,CAAC;;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACb,WAAU,GAAI,IAAI;QACvB,IAAIc,GAAE,GAAI3B,IAAG,GAAI,gBAAe,GAAIqB,GAAG,CAACO,GAAG;QAC3C7B,OAAO,CAAC8B,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACjB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACkB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAiB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAC5B,IAAI,CAACC,WAAU,GAAI2B,GAAG;MAC3B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACAA,QAAQA,CAAA,EAAG;MACT,IAAIoB,IAAG,GAAI;QACNhC,MAAM,EAAC,IAAI,CAACD,OAAO,CAACC,MAAM;QACtCC,KAAK,EAAC,IAAI,CAACF,OAAO,CAACE,KAAK;QACfC,OAAO,EAAE,IAAI,CAACH,OAAO,CAACG,OAAO;QACtC+B;MAEO,CAAC;MACD,IAAI,CAACzB,WAAU,GAAI,IAAI;MACvB,IAAIc,GAAE,GAAI3B,IAAG,GAAI,0BAAyB,GAAI,IAAI,CAACQ,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACtGX,OAAO,CAAC8B,IAAI,CAACF,GAAG,EAAEU,IAAI,CAAC,CAACX,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACS,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACjC,IAAI,CAACG,UAAS,GAAImB,GAAG,CAACY,KAAK;QAChC,IAAI,CAAC3B,QAAO,GAAIe,GAAG,CAACS,OAAO;QAC3B,IAAI,CAAC1B,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IACQ;IACT8B,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC1B,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACA2B,UAAUA,CAACxB,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACwB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,cAAc;QACnBJ,KAAK,EAAE;UACJK,EAAE,EAAE3B,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAqB,UAAUA,CAAC7B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAACwB,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,YAAY;QACjBJ,KAAK,EAAE;UACJK,EAAE,EAAE3B,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ;EACF;AACN"}]}