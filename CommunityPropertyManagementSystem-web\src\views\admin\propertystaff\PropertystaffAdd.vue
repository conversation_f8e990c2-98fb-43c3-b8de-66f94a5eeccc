﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="用户名" prop="plname">
<el-input v-model="formData.plname" placeholder="用户名"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="登录密码" prop="pword">
<el-input v-model="formData.pword" placeholder="登录密码"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="姓名" prop="pname">
<el-input v-model="formData.pname" placeholder="姓名"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="性别" prop="gender">
<el-radio-group v-model="formData.gender">
<el-radio label="男">
男
</el-radio>
<el-radio label="女">
女
</el-radio>
</el-radio-group>
</el-form-item>
<el-form-item label="年龄" prop="age">
<el-input v-model="formData.age" placeholder="年龄"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="联系方式" prop="contact">
<el-input v-model="formData.contact" placeholder="联系方式"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item label="联系地址" prop="address">
<el-input v-model="formData.address" placeholder="联系地址"  style="width:50%;" ></el-input>
</el-form-item>

<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'PropertystaffAdd',
  components: {
    
  },  
    data() {
      return {   
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          plname: [{ required: true, message: '请输入用户名', trigger: 'blur' },
],          pword: [{ required: true, message: '请输入登录密码', trigger: 'blur' },
],          pname: [{ required: true, message: '请输入姓名', trigger: 'blur' },
],          gender: [{ required: true, message: '请输入性别', trigger: 'blur' },
],          age: [{ required: true, message: '请输入年龄', trigger: 'blur' },
],          contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },
],          address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },
],          status: [{ required: true, message: '请输入审核状态', trigger: 'blur' },
],        },

      };
    },
    mounted() {
    
    },

 
    methods: {    
   // 添加
    save() {       
         this.$refs["formDataRef"].validate((valid) => { //验证表单
           if (valid) {
             let url = base + "/propertystaff/add";
             this.btnLoading = true;
             this.formData.status = '待审核';
             request.post(url, this.formData).then((res) => { //发送请求         
               if (res.code == 200) {
                 this.$message({
                   message: "操作成功",
                   type: "success",
                   offset: 320,
                 });              
                this.$router.push({
                path: "/PropertystaffManage",
                });
               } else {
                 this.$message({
                   message: res.msg,
                   type: "error",
                   offset: 320,
                 });
               }
               this.btnLoading=false;
             });
           }        
           
         });
    },
    
       // 返回
        goBack() {
          this.$router.push({
            path: "/PropertystaffManage",
          });
        },       
              
          
           
           
      },
}

</script>
<style scoped>
</style>
 

