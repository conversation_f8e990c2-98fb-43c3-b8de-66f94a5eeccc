{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1c2VyTG5hbWU6ICIiLAogICAgICByb2xlOiAiIgogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLnVzZXJMbmFtZSA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInVzZXJMbmFtZSIpOwogICAgdGhpcy5yb2xlID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgicm9sZSIpOwogIH0KfTs="}, {"version": 3, "names": ["data", "userLname", "role", "mounted", "sessionStorage", "getItem"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue"], "sourcesContent": ["<template>\r\n\r\n                 \r\n  \r\n\r\n  <div style=\"width: 100%;line-height: 30px;text-align: center; padding: 100px;\" id=\"home\">\r\n\r\n\r\n    账号：<b style=\"color: red;\">{{ userLname }}</b>，\r\n    身份：<b style=\"color: red;\">{{ role }}</b><br>\r\n\r\n\r\n\r\n    您好，欢迎使用小区物业管理系统！<br>\r\n\r\n\r\n  </div>\r\n\r\n\r\n\r\n \r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        userLname: \"\",\r\n        role: \"\",\r\n      };\r\n    },\r\n    mounted() {\r\n      this.userLname = sessionStorage.getItem(\"userLname\");\r\n      this.role = sessionStorage.getItem(\"role\");  \r\n\r\n    },\r\n  };\r\n\r\n</script>\r\n\r\n<style scoped></style>\r\n\r\n"], "mappings": "AAwBE,eAAe;EACbA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACF,SAAQ,GAAIG,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACH,IAAG,GAAIE,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;EAE5C;AACF,CAAC"}]}