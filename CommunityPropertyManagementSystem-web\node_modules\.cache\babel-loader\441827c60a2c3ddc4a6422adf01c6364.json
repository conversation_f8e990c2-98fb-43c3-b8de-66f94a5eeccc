{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749136123922}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "data", "userLname", "role", "statistics", "loading", "mounted", "sessionStorage", "getItem", "loadStatistics", "methods", "url", "params", "user", "JSON", "parse", "userId", "uid", "response", "post", "code", "resdata", "$message", "message", "msg", "type", "error", "console"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue"], "sourcesContent": ["<template>\r\n  <div id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div class=\"welcome-section\">\r\n      <h2 class=\"welcome-title\">欢迎使用小区物业管理系统</h2>\r\n      <p class=\"welcome-info\">\r\n        账号：<b style=\"color: #409EFF;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #409EFF;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片区域 -->\r\n    <div class=\"statistics-container\">\r\n      <div class=\"row\">\r\n        <!-- 管理员统计卡片 -->\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card staff-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-person-badge-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.staffCount || 0 }}</h3>\r\n              <p>工作人员数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 工作人员统计卡片 -->\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card announcement-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-megaphone-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.announcementCount || 0 }}</h3>\r\n              <p>公告信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户统计卡片 -->\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card house-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-house-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.houseCount || 0 }}</h3>\r\n              <p>房屋信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      statistics: {},\r\n      loading: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        let url = \"\";\r\n        let params = {};\r\n\r\n        if (this.role === \"管理员\") {\r\n          url = base + \"/statistics/admin\";\r\n        } else if (this.role === \"工作人员\") {\r\n          url = base + \"/statistics/staff\";\r\n        } else if (this.role === \"用户\") {\r\n          url = base + \"/statistics/usertotal\";\r\n          // 获取当前用户ID\r\n          const user = JSON.parse(sessionStorage.getItem(\"user\") || \"{}\");\r\n          params.userId = user.uid;\r\n        }\r\n\r\n        if (url) {\r\n          const response = await request.post(url, params);\r\n          if (response.code === 200) {\r\n            this.statistics = response.resdata;\r\n          } else {\r\n            this.$message({\r\n              message: response.msg || \"获取统计数据失败\",\r\n              type: \"error\",\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取统计数据失败:\", error);\r\n        this.$message({\r\n          message: \"获取统计数据失败\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n#home {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.welcome-section {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 30px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 15px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 2.5rem;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.welcome-info {\r\n  font-size: 1.2rem;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.statistics-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n  padding: 15px;\r\n  flex: 1;\r\n  min-width: 280px;\r\n}\r\n\r\n.col-md-4 {\r\n  flex: 0 0 33.333333%;\r\n}\r\n\r\n.col-md-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.col-lg-3 {\r\n  flex: 0 0 25%;\r\n}\r\n\r\n.col-lg-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 120px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));\r\n}\r\n\r\n.stat-icon {\r\n  font-size: 3rem;\r\n  margin-right: 20px;\r\n  color: var(--card-color);\r\n  opacity: 0.8;\r\n}\r\n\r\n.stat-content h3 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 5px 0;\r\n  color: #2c3e50;\r\n}\r\n\r\n.stat-content p {\r\n  font-size: 1rem;\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 不同卡片的颜色主题 */\r\n.user-card {\r\n  --card-color: #3498db;\r\n  --card-color-light: #5dade2;\r\n}\r\n\r\n.staff-card {\r\n  --card-color: #2ecc71;\r\n  --card-color-light: #58d68d;\r\n}\r\n\r\n.announcement-card {\r\n  --card-color: #f39c12;\r\n  --card-color-light: #f8c471;\r\n}\r\n\r\n.complaint-card {\r\n  --card-color: #e74c3c;\r\n  --card-color-light: #ec7063;\r\n}\r\n\r\n.expense-card {\r\n  --card-color: #9b59b6;\r\n  --card-color-light: #bb8fce;\r\n}\r\n\r\n.house-card {\r\n  --card-color: #1abc9c;\r\n  --card-color-light: #5dccb4;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n    flex: 0 0 100%;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .stat-card {\r\n    height: auto;\r\n    min-height: 100px;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    font-size: 2.5rem;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stat-content h3 {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  #home {\r\n    padding: 10px;\r\n  }\r\n\r\n  .welcome-section {\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .welcome-info {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    height: auto;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": "AAkIA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AAEnD,eAAe;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,CAAC,CAAC;MACdC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACJ,SAAQ,GAAIK,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACL,IAAG,GAAII,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IAC1C,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB,CAAC;EACDC,OAAO,EAAE;IACP;IACA,MAAMD,cAAcA,CAAA,EAAG;MACrB,IAAI,CAACJ,OAAM,GAAI,IAAI;MACnB,IAAI;QACF,IAAIM,GAAE,GAAI,EAAE;QACZ,IAAIC,MAAK,GAAI,CAAC,CAAC;QAEf,IAAI,IAAI,CAACT,IAAG,KAAM,KAAK,EAAE;UACvBQ,GAAE,GAAIX,IAAG,GAAI,mBAAmB;QAClC,OAAO,IAAI,IAAI,CAACG,IAAG,KAAM,MAAM,EAAE;UAC/BQ,GAAE,GAAIX,IAAG,GAAI,mBAAmB;QAClC,OAAO,IAAI,IAAI,CAACG,IAAG,KAAM,IAAI,EAAE;UAC7BQ,GAAE,GAAIX,IAAG,GAAI,uBAAuB;UACpC;UACA,MAAMa,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACR,cAAc,CAACC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;UAC/DI,MAAM,CAACI,MAAK,GAAIH,IAAI,CAACI,GAAG;QAC1B;QAEA,IAAIN,GAAG,EAAE;UACP,MAAMO,QAAO,GAAI,MAAMnB,OAAO,CAACoB,IAAI,CAACR,GAAG,EAAEC,MAAM,CAAC;UAChD,IAAIM,QAAQ,CAACE,IAAG,KAAM,GAAG,EAAE;YACzB,IAAI,CAAChB,UAAS,GAAIc,QAAQ,CAACG,OAAO;UACpC,OAAO;YACL,IAAI,CAACC,QAAQ,CAAC;cACZC,OAAO,EAAEL,QAAQ,CAACM,GAAE,IAAK,UAAU;cACnCC,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF;MACF,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACJ,QAAQ,CAAC;UACZC,OAAO,EAAE,UAAU;UACnBE,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,UAAU;QACR,IAAI,CAACpB,OAAM,GAAI,KAAK;MACtB;IACF;EACF;AACF,CAAC"}]}