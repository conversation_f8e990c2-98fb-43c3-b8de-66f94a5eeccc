{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue?vue&type=template&id=26084dc2&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue", "mtime": 1749134332807}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACvG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAElE,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;cAG7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;UAIP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAER,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <div class=\"container\">\n      <div class=\"login-container\">\n        <div class=\"login-box\">\n          <div class=\"text-center mb-4\">\n            <i class=\"bi bi-shield-lock text-primary\" style=\"font-size: 3rem\"></i>\n            <h2 class=\"mt-3\">小区物业管理系统</h2>\n            <p class=\"text-muted\">请输入您的账号和密码</p>\n          </div>\n          <form id=\"loginForm\">\n            <div class=\"form-floating mb-4\">\n              <input type=\"text\" class=\"form-control\" id=\"username\" placeholder=\"用户名\" v-model=\"loginModel.username\" />\n              <label for=\"username\"><i class=\"bi bi-person me-2\"></i>用户名</label>\n            </div>\n            <div class=\"form-floating mb-4\">\n              <input type=\"password\" class=\"form-control\" id=\"password\" placeholder=\"密码\"\n                v-model=\"loginModel.password\" />\n              <label for=\"password\"><i class=\"bi bi-key me-2\"></i>密码</label>\n            </div>\n            <div class=\"mb-4\">\n              <div class=\"role-selection\">\n                <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\n                <el-radio label=\"用户\" v-model=\"loginModel.radio\">用户</el-radio>\n                <el-radio label=\"工作人员\" v-model=\"loginModel.radio\">工作人员</el-radio>\n\n              </div>\n            </div>\n\n            <button type=\"button\" class=\"btn btn-primary w-100 py-2 mb-3\" @click=\"login\">\n              <i class=\"bi bi-box-arrow-in-right me-2\"></i>登录\n            </button>\n            <div style=\"text-align: center;\">\n              <a href=\"#\" @click=\"toreg\"> 用户注册 </a>&nbsp;&nbsp;|&nbsp;&nbsp;\n\n\n              <a href=\"#\" @click=\"toreg2\"> 工作人员注册 </a>\n            </div>\n\n\n\n          </form>\n\n        </div>\n        <div class=\"text-center text-white mt-4\">\n          <small>&copy; 管理系统. All rights reserved.</small>\n        </div>\n      </div>\n    </div>\n  </div>\n  <el-dialog title=\"用户注册\" v-model=\"userRegVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n    <el-form :model=\"userFormData\" label-width=\"20%\" ref=\"userFormDataRef\" :rules=\"userRules\" align=\"left\">\n      <el-form-item label=\"用户名\" prop=\"ulname\">\n        <el-input v-model=\"userFormData.ulname\" placeholder=\"用户名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"登录密码\" prop=\"loginpassword\">\n        <el-input type=\"password\" v-model=\"userFormData.loginpassword\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码\" prop=\"loginpassword2\">\n        <el-input type=\"password\" v-model=\"userFormData.loginpassword2\" placeholder=\"确认密码\"\n          style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"uname\">\n        <el-input v-model=\"userFormData.uname\" placeholder=\"姓名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"userFormData.gender\">\n          <el-radio label=\"男\">\n            男\n          </el-radio>\n          <el-radio label=\"女\">\n            女\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"userFormData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"userReg\" :loading=\"btnLoading\">注 册</el-button>\n      </el-form-item>\n    </el-form>\n  </el-dialog>\n\n  <el-dialog title=\"工作人员注册\" v-model=\"staffRegVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n    <el-form :model=\"staffFormData\" label-width=\"20%\" ref=\"staffFormDataRef\" :rules=\"staffRules\" align=\"left\">\n      <el-form-item label=\"用户名\" prop=\"plname\">\n        <el-input v-model=\"staffFormData.plname\" placeholder=\"用户名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"登录密码\" prop=\"pword\">\n        <el-input type=\"password\" v-model=\"staffFormData.pword\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码\" prop=\"pword2\">\n        <el-input type=\"password\" v-model=\"staffFormData.pword2\" placeholder=\"确认密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"pname\">\n        <el-input v-model=\"staffFormData.pname\" placeholder=\"姓名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"staffFormData.gender\">\n          <el-radio label=\"男\">\n            男\n          </el-radio>\n          <el-radio label=\"女\">\n            女\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"年龄\" prop=\"age\">\n        <el-input v-model=\"staffFormData.age\" placeholder=\"年龄\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"staffFormData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系地址\" prop=\"address\">\n        <el-input v-model=\"staffFormData.address\" placeholder=\"联系地址\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"staffReg\" :loading=\"btnLoading\">注 册</el-button>\n      </el-form-item>\n    </el-form>\n  </el-dialog>\n</template>\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n      add: true, //是否是添加\n      formVisible: false,\n      formData: {},\n\n      addrules: {\n        ulname: [{ required: true, message: '请输入用户名', trigger: 'blur' },],\n        loginpassword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n        loginpassword2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.loginpassword) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n        uname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n        uflag: [{ required: true, message: '请输入审核状态', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n      add: true, //是否是添加\n      formVisible: false,\n      formData: {},\n\n      addrules: {\n        plname: [{ required: true, message: '请输入用户名', trigger: 'blur' },],\n        pword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n        pword2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.pword) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n        pname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n        age: [{ required: true, message: '请输入年龄', trigger: 'blur' },],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\n        status: [{ required: true, message: '请输入审核状态', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n\n    };\n  },\n  mounted() { },\n  created() {\n\n  },\n  methods: {\n    login() {\n      let that = this;\n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }\n\n      this.loading = true;\n      var role = that.loginModel.radio; //获取身份\n      if (role == '管理员') {\n        let url = base + \"/admin/login\";\n        this.loginModel2.aname = this.loginModel.username;\n        this.loginModel2.password = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.aname);\n            sessionStorage.setItem(\"role\", \"管理员\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n      else if (role == '工作人员') {\n        let url = base + \"/propertystaff/login\";\n        this.loginModel2.plname = this.loginModel.username;\n        this.loginModel2.pword = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.plname);\n            sessionStorage.setItem(\"role\", \"工作人员\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n      else if (role == '用户') {\n        let url = base + \"/users/login\";\n        this.loginModel2.ulname = this.loginModel.username;\n        this.loginModel2.loginpassword = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.ulname);\n            sessionStorage.setItem(\"role\", \"用户\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n\n\n    },\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/users/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          this.formData.uflag = \"待审核\";\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/users/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          this.formData.status = \"待审核\";\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/propertystaff/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请登录！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n\n\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import url(../assets/css/ht_bootstrap.min.css);\n@import url(../assets/css/ht_bootstrap-icons.css);\n@import url(../assets/css/ht_style.css);\n\n.role-selection {\n  display: flex;\n  justify-content: center;\n  gap: 30px;\n  padding: 10px 0;\n}\n\n.role-option {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n}\n\n.role-option input[type='radio'] {\n  width: 18px;\n  height: 18px;\n  margin: 0;\n  cursor: pointer;\n}\n\n.role-option label {\n  margin: 0;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #6e707e;\n}\n\n.role-option:hover label {\n  color: var(--primary-color);\n}\n</style>\n"]}]}