{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue", "mtime": 1749135914930}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEhlYWRlciBmcm9tICcuLi9jb21wb25lbnRzL0hlYWRlcic7CmltcG9ydCBMZWZ0TWVudSBmcm9tICcuLi9jb21wb25lbnRzL0xlZnRNZW51JzsKaW1wb3J0IHsgRWxDb25maWdQcm92aWRlciB9IGZyb20gJ2VsZW1lbnQtcGx1cyc7CmltcG9ydCB6aENuIGZyb20gJ2VsZW1lbnQtcGx1cy9saWIvbG9jYWxlL2xhbmcvemgtY24nOwppbXBvcnQgJCBmcm9tICdqcXVlcnknOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ01haW5MYXlvdXQnLAogIGNvbXBvbmVudHM6IHsKICAgIEhlYWRlciwKICAgIExlZnRNZW51LAogICAgW0VsQ29uZmlnUHJvdmlkZXIubmFtZV06IEVsQ29uZmlnUHJvdmlkZXIKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2NhbGU6IHpoQ24KICAgIH07CiAgfSwKICBtb3VudGVkKCkge30sCiAgbWV0aG9kczoge30KfTs="}, {"version": 3, "names": ["Header", "LeftMenu", "ElConfigProvider", "zhCn", "$", "name", "components", "data", "locale", "mounted", "methods"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n  <el-config-provider :locale=\"locale\">\r\n    <div class=\"wrapper\" style=\"\">\r\n      <LeftMenu />\r\n\r\n      <div id=\"content\">\r\n        <Header class=\"header-fixed\" />\r\n\r\n        <div class=\"main-content\">\r\n          <div class=\"container-fluid p-4\">\r\n            <div class=\"page-container\">\r\n              <div class=\"content-container\">\r\n                <div class=\"page-header d-flex justify-content-between align-items-center\">\r\n                  <h5 class=\"page-title\">{{ this.$route.meta.title }}</h5>\r\n                </div>\r\n\r\n                <div class=\"table-responsive\"><router-view /></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-config-provider>\r\n</template>\r\n\r\n<script>\r\nimport Header from '../components/Header';\r\nimport LeftMenu from '../components/LeftMenu';\r\nimport { ElConfigProvider } from 'element-plus';\r\nimport zhCn from 'element-plus/lib/locale/lang/zh-cn';\r\nimport $ from 'jquery';\r\nexport default {\r\n  name: 'MainLayout',\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n  },\r\n\r\n  methods: {},\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n@import url(../assets/css/ht_bootstrap.min.css);\r\n@import url(../assets/css/ht_bootstrap-icons.css);\r\n@import url(../assets/css/ht_style.css);\r\n\r\n.wrapper {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  position: relative;\r\n}\r\n\r\n#content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  position: relative;\r\n}\r\n\r\n.header-fixed {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  left: var(--sidebar-width);\r\n  z-index: 100;\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  padding-top: var(--header-height);\r\n  overflow-y: auto;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.table-responsive {\r\n  position: relative;\r\n  z-index: 3;\r\n}\r\n\r\n.page-header {\r\n  margin-bottom: 1.5rem;\r\n  padding: 1rem 0;\r\n  border-bottom: 2px solid #eaecf4;\r\n  background: linear-gradient(to right, #ffffff, #f8f9fc);\r\n}\r\n\r\n.page-title {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  padding: 0.5rem 1rem;\r\n  position: relative;\r\n}\r\n\r\n.page-title::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 4px;\r\n  height: 24px;\r\n  background: var(--primary-color);\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n"], "mappings": "AA2BA,OAAOA,MAAK,MAAO,sBAAsB;AACzC,OAAOC,QAAO,MAAO,wBAAwB;AAC7C,SAASC,gBAAe,QAAS,cAAc;AAC/C,OAAOC,IAAG,MAAO,oCAAoC;AACrD,OAAOC,CAAA,MAAO,QAAQ;AACtB,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVN,MAAM;IACNC,QAAQ;IACR,CAACC,gBAAgB,CAACG,IAAI,GAAGH;EAC3B,CAAC;EACDK,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAEL;IACV,CAAC;EACH,CAAC;EACDM,OAAOA,CAAA,EAAG,CACV,CAAC;EAEDC,OAAO,EAAE,CAAC;AACZ,CAAC"}]}