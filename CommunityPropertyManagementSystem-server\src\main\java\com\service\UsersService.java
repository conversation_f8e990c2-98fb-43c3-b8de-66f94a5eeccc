package com.service;
import java.util.List;

import com.model.Users;
import com.util.PageBean;

public interface UsersService{
	
	//查询多条记录
	public List<Users> queryUsersList(Users users,PageBean page) throws Exception;
 
	//添加
	public int insertUsers(Users users) throws Exception ;
	
	//根据ID删除
	public int deleteUsers(int id) throws Exception ;
	
	//更新
	public int updateUsers(Users users) throws Exception ;
	
	//根据ID查询单条数据
	public Users queryUsersById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Users users);

}

