package com.model;
import java.util.List;

/**
* (expenseinfo)费用信息实体类
*/
public class Expenseinfo extends ComData{
	
	private static final long serialVersionUID = 458174353435537L;
	private Integer eid;    //费用id
	private String etitle;    //费用标题
	private String hno;    //门牌号
	private Object eamount;    //金额
	private String edescription;    //费用说明
	private String etime;    //添加时间
	private String epaymentstatus;    //缴纳状态

	public Integer getEid() {
		return eid;
	}

	public void setEid(Integer eid) {
		this.eid = eid;
	}

	public String getEtitle() {
		return etitle;
	}

	public void setEtitle(String etitle) {
		this.etitle = etitle;
	}

	public String getHno() {
		return hno;
	}

	public void setHno(String hno) {
		this.hno = hno;
	}

	public Object getEamount() {
		return eamount;
	}

	public void setEamount(Object eamount) {
		this.eamount = eamount;
	}

	public String getEdescription() {
		return edescription;
	}

	public void setEdescription(String edescription) {
		this.edescription = edescription;
	}

	public String getEtime() {
		return etime;
	}

	public void setEtime(String etime) {
		this.etime = etime;
	}

	public String getEpaymentstatus() {
		return epaymentstatus;
	}

	public void setEpaymentstatus(String epaymentstatus) {
		this.epaymentstatus = epaymentstatus;
	}

}

