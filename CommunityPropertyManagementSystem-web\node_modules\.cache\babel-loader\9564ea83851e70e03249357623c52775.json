{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749134881813}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTGVmdE1lbnUnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1c2VyTG5hbWU6ICcnLAogICAgICByb2xlOiAnJywKICAgICAgYWN0aXZlTWVudUl0ZW06ICdkYXNoYm9hcmQnLAogICAgICBvcGVuU3VibWVudXM6IFtdCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMudXNlckxuYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndXNlckxuYW1lJyk7CiAgICB0aGlzLnJvbGUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdyb2xlJyk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBzZXRBY3RpdmVNZW51SXRlbShtZW51SXRlbSkgewogICAgICB0aGlzLmFjdGl2ZU1lbnVJdGVtID0gbWVudUl0ZW07CiAgICB9LAogICAgdG9nZ2xlU3VibWVudShzdWJtZW51SWQpIHsKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLm9wZW5TdWJtZW51cy5pbmRleE9mKHN1Ym1lbnVJZCk7CiAgICAgIGlmIChpbmRleCA9PT0gLTEpIHsKICAgICAgICAvLyBDbG9zZSBvdGhlciBzdWJtZW51cyBiZWZvcmUgb3BlbmluZyBuZXcgb25lCiAgICAgICAgdGhpcy5vcGVuU3VibWVudXMgPSBbc3VibWVudUlkXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLm9wZW5TdWJtZW51cy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB9CiAgICB9LAogICAgZXhpdCgpIHsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k6YCA5Ye65ZCXPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyTG5hbWUnKTsKICAgICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCdyb2xlJyk7CiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy8nKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "userLname", "role", "activeMenuItem", "openSubmenus", "mounted", "sessionStorage", "getItem", "methods", "setActiveMenuItem", "menuItem", "toggleSubmenu", "submenuId", "index", "indexOf", "splice", "exit", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "$router", "push", "catch"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n  <!-- 侧边栏 -->\r\n  <nav id=\"sidebar\">\r\n    <div class=\"sidebar-header\">\r\n      <div class=\"d-flex align-items-center p-3\">\r\n        <i class=\"bi bi-shield-check text-white me-2\" style=\"font-size: 1.5rem\"></i>\r\n        <h5 class=\"mb-0\">小区物业管理系统</h5>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '管理员'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n\r\n\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '工作人员'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n          <li><router-link to=\"/usersInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu3\" @click.prevent=\"toggleSubmenu('userSubmenu3')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>公告管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu3') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu3') }\" id=\"userSubmenu3\">\r\n          <li><router-link to=\"/announcementsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage\"> <i class=\"bi bi-circle me-2\"></i>管理公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage2\"> <i class=\"bi bi-circle me-2\"></i>公告列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu4\" @click.prevent=\"toggleSubmenu('userSubmenu4')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>投诉建议管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu4') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu4') }\" id=\"userSubmenu4\">\r\n          <li><router-link to=\"/complaintsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage\"> <i class=\"bi bi-circle me-2\"></i>管理投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage2\"> <i class=\"bi bi-circle me-2\"></i>投诉建议列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu5\" @click.prevent=\"toggleSubmenu('userSubmenu5')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>房屋信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu5') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu5') }\" id=\"userSubmenu5\">\r\n          <li><router-link to=\"/houseAdd\"> <i class=\"bi bi-circle me-2\"></i>添加房屋信息</router-link></li>\r\n          <li><router-link to=\"/houseManage\"> <i class=\"bi bi-circle me-2\"></i>管理房屋信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu7\" @click.prevent=\"toggleSubmenu('userSubmenu7')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>费用信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu7') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu7') }\" id=\"userSubmenu7\">\r\n          <li><router-link to=\"/expenseinfoAdd\"> <i class=\"bi bi-circle me-2\"></i>添加费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage\"> <i class=\"bi bi-circle me-2\"></i>管理费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage2\"> <i class=\"bi bi-circle me-2\"></i>费用信息列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '用户'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n          <li><router-link to=\"/usersInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu3\" @click.prevent=\"toggleSubmenu('userSubmenu3')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>公告管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu3') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu3') }\" id=\"userSubmenu3\">\r\n          <li><router-link to=\"/announcementsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage\"> <i class=\"bi bi-circle me-2\"></i>管理公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage2\"> <i class=\"bi bi-circle me-2\"></i>公告列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu4\" @click.prevent=\"toggleSubmenu('userSubmenu4')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>投诉建议管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu4') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu4') }\" id=\"userSubmenu4\">\r\n          <li><router-link to=\"/complaintsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage\"> <i class=\"bi bi-circle me-2\"></i>管理投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage2\"> <i class=\"bi bi-circle me-2\"></i>投诉建议列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu5\" @click.prevent=\"toggleSubmenu('userSubmenu5')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>房屋信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu5') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu5') }\" id=\"userSubmenu5\">\r\n          <li><router-link to=\"/houseAdd\"> <i class=\"bi bi-circle me-2\"></i>添加房屋信息</router-link></li>\r\n          <li><router-link to=\"/houseManage\"> <i class=\"bi bi-circle me-2\"></i>管理房屋信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu7\" @click.prevent=\"toggleSubmenu('userSubmenu7')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>费用信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu7') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu7') }\" id=\"userSubmenu7\">\r\n          <li><router-link to=\"/expenseinfoAdd\"> <i class=\"bi bi-circle me-2\"></i>添加费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage\"> <i class=\"bi bi-circle me-2\"></i>管理费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage2\"> <i class=\"bi bi-circle me-2\"></i>费用信息列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n    <div class=\"sidebar-footer\">\r\n      <a href=\"javascript:void(0);\" @click=\"exit\" class=\"d-flex align-items-center text-white text-decoration-none p-3\">\r\n        <i class=\"bi bi-box-arrow-left me-2\"></i>\r\n        <span>退出登录</span>\r\n      </a>\r\n    </div>\r\n  </nav>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'LeftMenu',\r\n  data() {\r\n    return {\r\n      userLname: '',\r\n      role: '',\r\n      activeMenuItem: 'dashboard',\r\n      openSubmenus: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem('userLname');\r\n    this.role = sessionStorage.getItem('role');\r\n  },\r\n  methods: {\r\n    setActiveMenuItem(menuItem) {\r\n      this.activeMenuItem = menuItem;\r\n    },\r\n    toggleSubmenu(submenuId) {\r\n      const index = this.openSubmenus.indexOf(submenuId);\r\n      if (index === -1) {\r\n        // Close other submenus before opening new one\r\n        this.openSubmenus = [submenuId];\r\n      } else {\r\n        this.openSubmenus.splice(index, 1);\r\n      }\r\n    },\r\n    exit() {\r\n      this.$confirm('确认退出吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem('userLname');\r\n          sessionStorage.removeItem('role');\r\n          this.$router.push('/');\r\n        })\r\n        .catch(() => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.bi-chevron-down {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.rotate-180 {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.collapse {\r\n  transition: all 0.3s ease-out;\r\n}\r\n\r\n.collapse.show {\r\n  display: block;\r\n}\r\n\r\n.sub-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sub-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sub-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.left-menu {\r\n  width: 250px;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100vh;\r\n  z-index: 999;\r\n  background: #fff;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n#content {\r\n  width: calc(100% - 250px);\r\n  margin-left: 250px;\r\n  transition: all 0.3s;\r\n  min-height: 100vh;\r\n}\r\n\r\n:global(.sidebar-collapsed) #content {\r\n  margin-left: 0;\r\n  width: 100%;\r\n}\r\n\r\n:global(.sidebar-collapsed) .left-menu {\r\n  margin-left: -250px;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";AA0TA,eAAe;EACbA,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,cAAc,EAAE,WAAW;MAC3BC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACJ,SAAQ,GAAIK,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACL,IAAG,GAAII,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;EAC5C,CAAC;EACDC,OAAO,EAAE;IACPC,iBAAiBA,CAACC,QAAQ,EAAE;MAC1B,IAAI,CAACP,cAAa,GAAIO,QAAQ;IAChC,CAAC;IACDC,aAAaA,CAACC,SAAS,EAAE;MACvB,MAAMC,KAAI,GAAI,IAAI,CAACT,YAAY,CAACU,OAAO,CAACF,SAAS,CAAC;MAClD,IAAIC,KAAI,KAAM,CAAC,CAAC,EAAE;QAChB;QACA,IAAI,CAACT,YAAW,GAAI,CAACQ,SAAS,CAAC;MACjC,OAAO;QACL,IAAI,CAACR,YAAY,CAACW,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACpC;IACF,CAAC;IACDG,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACVf,cAAc,CAACgB,UAAU,CAAC,WAAW,CAAC;QACtChB,cAAc,CAACgB,UAAU,CAAC,MAAM,CAAC;QACjC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;MACxB,CAAC,EACAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB;EACF;AACF,CAAC"}]}