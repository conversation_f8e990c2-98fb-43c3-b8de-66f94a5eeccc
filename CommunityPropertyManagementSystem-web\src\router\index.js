﻿import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Login',
    component: () => import('../views/Login'),
    meta: {
      requireAuth: false
    }
  },

  {
    path: '/main',
    name: 'Main',
    component: () => import('../views/Main'),
    redirect: "/home",
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('../views/admin/Home'),
        meta: {
          requireAuth: true,title:'首页'
        }

      },
     
      {
      path: '/usersAdd',
      name: 'UsersAdd',
      component: () => import('../views/admin/users/UsersAdd'),
      meta: { requiresAuth: true,title: '用户添加' }
    },
 {
      path: '/usersEdit',
      name: 'UsersEdit',
      component: () => import('../views/admin/users/UsersEdit'),
      meta: { requiresAuth: true,title: '用户修改' }
    },
 {
      path: '/usersManage',
      name: 'UsersManage',
      component: () => import('../views/admin/users/UsersManage'),
      meta: { requiresAuth: true,title: '用户管理' }
    },
{
      path: '/usersManage2',
      name: 'UsersManage2',
      component: () => import('../views/admin/users/UsersManage2'),
      meta: { requiresAuth: true,title: '用户列表' }
    },
{
    path: '/usersDetail',
    name: 'UsersDetail',
    component: () => import('../views/admin/users/UsersDetail'),
    meta: { requiresAuth: true,title: '用户详情' }
  },
{
      path: '/usersInfo',
      name: 'UsersInfo',
      component: () => import('../views/admin/users/UsersInfo'),
      meta: { requiresAuth: true,title: '修改个人信息' }
    },
{
      path: '/announcementsAdd',
      name: 'AnnouncementsAdd',
      component: () => import('../views/admin/announcements/AnnouncementsAdd'),
      meta: { requiresAuth: true,title: '公告添加' }
    },
 {
      path: '/announcementsEdit',
      name: 'AnnouncementsEdit',
      component: () => import('../views/admin/announcements/AnnouncementsEdit'),
      meta: { requiresAuth: true,title: '公告修改' }
    },
 {
      path: '/announcementsManage',
      name: 'AnnouncementsManage',
      component: () => import('../views/admin/announcements/AnnouncementsManage'),
      meta: { requiresAuth: true,title: '公告管理' }
    },
{
      path: '/announcementsManage2',
      name: 'AnnouncementsManage2',
      component: () => import('../views/admin/announcements/AnnouncementsManage2'),
      meta: { requiresAuth: true,title: '公告列表' }
    },
{
    path: '/announcementsDetail',
    name: 'AnnouncementsDetail',
    component: () => import('../views/admin/announcements/AnnouncementsDetail'),
    meta: { requiresAuth: true,title: '公告详情' }
  },
{
      path: '/complaintsAdd',
      name: 'ComplaintsAdd',
      component: () => import('../views/admin/complaints/ComplaintsAdd'),
      meta: { requiresAuth: true,title: '提交投诉建议' }
    },
 {
      path: '/complaintsEdit',
      name: 'ComplaintsEdit',
      component: () => import('../views/admin/complaints/ComplaintsEdit'),
      meta: { requiresAuth: true,title: '投诉建议修改' }
    },
 {
      path: '/complaintsManage',
      name: 'ComplaintsManage',
      component: () => import('../views/admin/complaints/ComplaintsManage'),
      meta: { requiresAuth: true,title: '投诉建议管理' }
    },
{
      path: '/complaintsManage2',
      name: 'ComplaintsManage2',
      component: () => import('../views/admin/complaints/ComplaintsManage2'),
      meta: { requiresAuth: true,title: '我的投诉建议' }
    },
{
    path: '/complaintsDetail',
    name: 'ComplaintsDetail',
    component: () => import('../views/admin/complaints/ComplaintsDetail'),
    meta: { requiresAuth: true,title: '投诉建议详情' }
  },
{
      path: '/houseAdd',
      name: 'HouseAdd',
      component: () => import('../views/admin/house/HouseAdd'),
      meta: { requiresAuth: true,title: '房屋信息添加' }
    },
 {
      path: '/houseEdit',
      name: 'HouseEdit',
      component: () => import('../views/admin/house/HouseEdit'),
      meta: { requiresAuth: true,title: '房屋信息修改' }
    },
 {
      path: '/houseManage',
      name: 'HouseManage',
      component: () => import('../views/admin/house/HouseManage'),
      meta: { requiresAuth: true,title: '房屋信息管理' }
      },
{
      path: '/houseManage2',
      name: 'HouseManage2',
      component: () => import('../views/admin/house/HouseManage2'),
      meta: { requiresAuth: true,title: '房屋信息列表' }
    },
{
    path: '/houseDetail',
    name: 'HouseDetail',
    component: () => import('../views/admin/house/HouseDetail'),
    meta: { requiresAuth: true,title: '房屋信息详情' }
  },
{
      path: '/propertystaffAdd',
      name: 'PropertystaffAdd',
      component: () => import('../views/admin/propertystaff/PropertystaffAdd'),
      meta: { requiresAuth: true,title: '工作人员添加' }
    },
 {
      path: '/propertystaffEdit',
      name: 'PropertystaffEdit',
      component: () => import('../views/admin/propertystaff/PropertystaffEdit'),
      meta: { requiresAuth: true,title: '工作人员修改' }
    },
 {
      path: '/propertystaffManage',
      name: 'PropertystaffManage',
      component: () => import('../views/admin/propertystaff/PropertystaffManage'),
      meta: { requiresAuth: true,title: '工作人员管理' }
      },
{
      path: '/propertystaffManage2',
      name: 'PropertystaffManage2',
      component: () => import('../views/admin/propertystaff/PropertystaffManage2'),
      meta: { requiresAuth: true,title: '工作人员列表' }
    },
{
    path: '/propertystaffDetail',
    name: 'PropertystaffDetail',
    component: () => import('../views/admin/propertystaff/PropertystaffDetail'),
    meta: { requiresAuth: true,title: '工作人员详情' }
  },
{
      path: '/propertystaffInfo',
      name: 'PropertystaffInfo',
      component: () => import('../views/admin/propertystaff/PropertystaffInfo'),
      meta: { requiresAuth: true,title: '修改个人信息' }
    },
{
      path: '/expenseinfoAdd',
      name: 'ExpenseinfoAdd',
      component: () => import('../views/admin/expenseinfo/ExpenseinfoAdd'),
      meta: { requiresAuth: true,title: '费用信息添加' }
    },
 {
      path: '/expenseinfoEdit',
      name: 'ExpenseinfoEdit',
      component: () => import('../views/admin/expenseinfo/ExpenseinfoEdit'),
      meta: { requiresAuth: true,title: '费用信息修改' }
    },
 {
      path: '/expenseinfoManage',
      name: 'ExpenseinfoManage',
      component: () => import('../views/admin/expenseinfo/ExpenseinfoManage'),
      meta: { requiresAuth: true,title: '费用信息管理' }
    },
{
      path: '/expenseinfoManage2',
      name: 'ExpenseinfoManage2',
      component: () => import('../views/admin/expenseinfo/ExpenseinfoManage2'),
      meta: { requiresAuth: true,title: '费用信息列表' }
    },
{
    path: '/expenseinfoDetail',
    name: 'ExpenseinfoDetail',
    component: () => import('../views/admin/expenseinfo/ExpenseinfoDetail'),
    meta: { requiresAuth: true,title: '费用信息详情' }
  },

     {
          path: '/password',
          name: 'Password',
          component: () => import('../views/admin/system/Password'),
          meta: {
            requireAuth: true,title:'修改密码'
          }
     },
    ]
  },
    
  
]



const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})



router.beforeEach((to, from, next) => {
  if (to.path == '/') {
    sessionStorage.removeItem('userLname');
    sessionStorage.removeItem('role');
  }
  let currentUser = sessionStorage.getItem('userLname');
  console.log(to + "  to.meta.requireAuth");

  if (to.meta.requireAuth) {
    if (!currentUser && to.path != '/login') {
      next({ path: '/' });
    } else {
      next();
    }
  } else {

    next();
  }
})

export default router


