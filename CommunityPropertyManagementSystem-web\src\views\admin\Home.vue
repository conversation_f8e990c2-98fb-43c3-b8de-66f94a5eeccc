﻿<template>
  <div id="home">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <h2 class="welcome-title">欢迎使用小区物业管理系统</h2>
      <p class="welcome-info">
        账号：<b style="color: #409EFF;">{{ userLname }}</b>，
        身份：<b style="color: #409EFF;">{{ role }}</b>
      </p>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-container">
      <div class="row">
        <!-- 管理员统计卡片 -->
        <div v-if="role === '管理员'" class="col-md-6 col-lg-6">
          <div class="stat-card user-card">
            <div class="stat-icon">
              <i class="bi bi-people-fill"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.userCount || 0 }}</h3>
              <p>用户数量</p>
            </div>
          </div>
        </div>

        <div v-if="role === '管理员'" class="col-md-6 col-lg-6">
          <div class="stat-card staff-card">
            <div class="stat-icon">
              <i class="bi bi-person-badge-fill"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.staffCount || 0 }}</h3>
              <p>工作人员数量</p>
            </div>
          </div>
        </div>

        <!-- 工作人员统计卡片 -->
        <div v-if="role === '工作人员'" class="col-md-6 col-lg-3">
          <div class="stat-card user-card">
            <div class="stat-icon">
              <i class="bi bi-people-fill"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.userCount || 0 }}</h3>
              <p>用户数量</p>
            </div>
          </div>
        </div>

        <div v-if="role === '工作人员'" class="col-md-6 col-lg-3">
          <div class="stat-card announcement-card">
            <div class="stat-icon">
              <i class="bi bi-megaphone-fill"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.announcementCount || 0 }}</h3>
              <p>公告信息数量</p>
            </div>
          </div>
        </div>

        <div v-if="role === '工作人员'" class="col-md-6 col-lg-3">
          <div class="stat-card complaint-card">
            <div class="stat-icon">
              <i class="bi bi-chat-square-text-fill"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.complaintCount || 0 }}</h3>
              <p>投诉建议数量</p>
            </div>
          </div>
        </div>

        <div v-if="role === '工作人员'" class="col-md-6 col-lg-3">
          <div class="stat-card expense-card">
            <div class="stat-icon">
              <i class="bi bi-currency-dollar"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.expenseCount || 0 }}</h3>
              <p>费用信息数量</p>
            </div>
          </div>
        </div>

        <!-- 用户统计卡片 -->
        <div v-if="role === '用户'" class="col-md-4">
          <div class="stat-card house-card">
            <div class="stat-icon">
              <i class="bi bi-house-fill"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.houseCount || 0 }}</h3>
              <p>房屋信息数量</p>
            </div>
          </div>
        </div>

        <div v-if="role === '用户'" class="col-md-4">
          <div class="stat-card expense-card">
            <div class="stat-icon">
              <i class="bi bi-currency-dollar"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.expenseCount || 0 }}</h3>
              <p>费用信息数量</p>
            </div>
          </div>
        </div>

        <div v-if="role === '用户'" class="col-md-4">
          <div class="stat-card complaint-card">
            <div class="stat-icon">
              <i class="bi bi-chat-square-text-fill"></i>
            </div>
            <div class="stat-content">
              <h3>{{ statistics.complaintCount || 0 }}</h3>
              <p>投诉建议数量</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request, { base } from "../../../utils/http";

export default {
  data() {
    return {
      userLname: "",
      role: "",
      statistics: {},
      loading: false,
    };
  },
  mounted() {
    this.userLname = sessionStorage.getItem("userLname");
    this.role = sessionStorage.getItem("role");
    this.loadStatistics();
  },
  methods: {
    // 加载统计数据
    async loadStatistics() {
      this.loading = true;
      try {
        let url = "";
        let params = {};

        if (this.role === "管理员") {
          url = base + "/statistics/admin";
        } else if (this.role === "工作人员") {
          url = base + "/statistics/staff";
        } else if (this.role === "用户") {
          url = base + "/statistics/usertotal";
          // 获取当前用户ID
          const user = JSON.parse(sessionStorage.getItem("user") || "{}");
          params.userId = user.uid;
        }

        if (url) {
          const response = await request.post(url, params);
          if (response.code === 200) {
            this.statistics = response.resdata;
          } else {
            this.$message({
              message: response.msg || "获取统计数据失败",
              type: "error",
            });
          }
        }
      } catch (error) {
        console.error("获取统计数据失败:", error);
        this.$message({
          message: "获取统计数据失败",
          type: "error",
        });
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped>
#home {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-info {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

.statistics-container {
  max-width: 1200px;
  margin: 0 auto;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: -15px;
}

.col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {
  padding: 15px;
  flex: 1;
  min-width: 280px;
}

.col-md-4 {
  flex: 0 0 33.333333%;
}

.col-md-6 {
  flex: 0 0 50%;
}

.col-lg-3 {
  flex: 0 0 25%;
}

.col-lg-6 {
  flex: 0 0 50%;
}

.stat-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  height: 120px;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
}

.stat-icon {
  font-size: 3rem;
  margin-right: 20px;
  color: var(--card-color);
  opacity: 0.8;
}

.stat-content h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.stat-content p {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
  font-weight: 500;
}

/* 不同卡片的颜色主题 */
.user-card {
  --card-color: #3498db;
  --card-color-light: #5dade2;
}

.staff-card {
  --card-color: #2ecc71;
  --card-color-light: #58d68d;
}

.announcement-card {
  --card-color: #f39c12;
  --card-color-light: #f8c471;
}

.complaint-card {
  --card-color: #e74c3c;
  --card-color-light: #ec7063;
}

.expense-card {
  --card-color: #9b59b6;
  --card-color-light: #bb8fce;
}

.house-card {
  --card-color: #1abc9c;
  --card-color-light: #5dccb4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .stat-card {
    height: auto;
    min-height: 100px;
    padding: 20px;
  }

  .stat-icon {
    font-size: 2.5rem;
    margin-right: 15px;
  }

  .stat-content h3 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  #home {
    padding: 10px;
  }

  .welcome-section {
    padding: 20px;
    margin-bottom: 20px;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .welcome-info {
    font-size: 1rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    height: auto;
    padding: 20px;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>

