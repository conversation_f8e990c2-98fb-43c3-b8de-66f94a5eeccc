﻿<template>
  <!-- 侧边栏 -->
  <nav id="sidebar">
    <div class="sidebar-header">
      <div class="d-flex align-items-center p-3">
        <i class="bi bi-shield-check text-white me-2" style="font-size: 1.5rem"></i>
        <h5 class="mb-0"><a href="/main" style="color: #fff;text-decoration: none;">小区物业管理系统</a></h5>
      </div>
    </div>



    <ul class="list-unstyled components" v-show="role == '管理员'">
      <li>
        <a href="#userSubmenu1" @click.prevent="toggleSubmenu('userSubmenu1')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>用户管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu1') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu1') }" id="userSubmenu1">
          <li><router-link to="/usersAdd"> <i class="bi bi-circle me-2"></i>添加用户</router-link></li>
                <li><router-link to="/usersManage2"> <i class="bi bi-circle me-2"></i>审核用户</router-link></li>
          <li><router-link to="/usersManage"> <i class="bi bi-circle me-2"></i>管理用户</router-link></li>
    

        </ul>
      </li>

      <li>
        <a href="#userSubmenu6" @click.prevent="toggleSubmenu('userSubmenu6')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>工作人员管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu6') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu6') }" id="userSubmenu6">
          <li><router-link to="/propertystaffAdd"> <i class="bi bi-circle me-2"></i>添加工作人员</router-link></li>
                    <li><router-link to="/propertystaffManage2"> <i class="bi bi-circle me-2"></i>审核工作人员</router-link></li>
          <li><router-link to="/propertystaffManage"> <i class="bi bi-circle me-2"></i>管理工作人员</router-link></li>
   

        </ul>
      </li>




      <li>
        <a href="#userSubmenu26" @click.prevent="toggleSubmenu('userSubmenu26')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>系统管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu26') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu26') }" id="userSubmenu26">

          <li><router-link to="/password"> <i class="bi bi-circle me-2"></i>修改密码</router-link></li>

        </ul>
      </li>

    </ul>


    <ul class="list-unstyled components" v-show="role == '工作人员'">
      <li>
        <a href="#userSubmenu1" @click.prevent="toggleSubmenu('userSubmenu1')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>用户管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu1') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu1') }" id="userSubmenu1">
          <li><router-link to="/usersAdd"> <i class="bi bi-circle me-2"></i>添加用户</router-link></li>
          <li><router-link to="/usersManage"> <i class="bi bi-circle me-2"></i>管理用户</router-link></li>
          <li><router-link to="/usersManage2"> <i class="bi bi-circle me-2"></i>用户列表</router-link></li>
          <li><router-link to="/usersInfo"> <i class="bi bi-circle me-2"></i>修改个人信息</router-link></li>

        </ul>
      </li>
      <li>
        <a href="#userSubmenu3" @click.prevent="toggleSubmenu('userSubmenu3')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>公告管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu3') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu3') }" id="userSubmenu3">
          <li><router-link to="/announcementsAdd"> <i class="bi bi-circle me-2"></i>添加公告</router-link></li>
          <li><router-link to="/announcementsManage"> <i class="bi bi-circle me-2"></i>管理公告</router-link></li>
          <li><router-link to="/announcementsManage2"> <i class="bi bi-circle me-2"></i>公告列表</router-link></li>

        </ul>
      </li>
      <li>
        <a href="#userSubmenu4" @click.prevent="toggleSubmenu('userSubmenu4')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>投诉建议管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu4') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu4') }" id="userSubmenu4">
          <li><router-link to="/complaintsAdd"> <i class="bi bi-circle me-2"></i>添加投诉建议</router-link></li>
          <li><router-link to="/complaintsManage"> <i class="bi bi-circle me-2"></i>管理投诉建议</router-link></li>
          <li><router-link to="/complaintsManage2"> <i class="bi bi-circle me-2"></i>投诉建议列表</router-link></li>

        </ul>
      </li>
      <li>
        <a href="#userSubmenu5" @click.prevent="toggleSubmenu('userSubmenu5')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>房屋信息管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu5') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu5') }" id="userSubmenu5">
          <li><router-link to="/houseAdd"> <i class="bi bi-circle me-2"></i>添加房屋信息</router-link></li>
          <li><router-link to="/houseManage"> <i class="bi bi-circle me-2"></i>管理房屋信息</router-link></li>

        </ul>
      </li>
      <li>
        <a href="#userSubmenu6" @click.prevent="toggleSubmenu('userSubmenu6')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>工作人员管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu6') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu6') }" id="userSubmenu6">
          <li><router-link to="/propertystaffAdd"> <i class="bi bi-circle me-2"></i>添加工作人员</router-link></li>
          <li><router-link to="/propertystaffManage"> <i class="bi bi-circle me-2"></i>管理工作人员</router-link></li>
          <li><router-link to="/propertystaffInfo"> <i class="bi bi-circle me-2"></i>修改个人信息</router-link></li>

        </ul>
      </li>
      <li>
        <a href="#userSubmenu7" @click.prevent="toggleSubmenu('userSubmenu7')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>费用信息管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu7') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu7') }" id="userSubmenu7">
          <li><router-link to="/expenseinfoAdd"> <i class="bi bi-circle me-2"></i>添加费用信息</router-link></li>
          <li><router-link to="/expenseinfoManage"> <i class="bi bi-circle me-2"></i>管理费用信息</router-link></li>
          <li><router-link to="/expenseinfoManage2"> <i class="bi bi-circle me-2"></i>费用信息列表</router-link></li>

        </ul>
      </li>

      <li>
        <a href="#userSubmenu26" @click.prevent="toggleSubmenu('userSubmenu26')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>系统管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu26') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu26') }" id="userSubmenu26">

          <li><router-link to="/password"> <i class="bi bi-circle me-2"></i>修改密码</router-link></li>

        </ul>
      </li>

    </ul>



    <ul class="list-unstyled components" v-show="role == '用户'">

      <li>
        <a href="#userSubmenu3" @click.prevent="toggleSubmenu('userSubmenu3')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>公告管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu3') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu3') }" id="userSubmenu3">
  
          <li><router-link to="/announcementsManage2"> <i class="bi bi-circle me-2"></i>公告列表</router-link></li>

        </ul>
      </li>

            <li>
        <a href="#userSubmenu5" @click.prevent="toggleSubmenu('userSubmenu5')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>房屋信息管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu5') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu5') }" id="userSubmenu5">
          <li><router-link to="/houseAdd"> <i class="bi bi-circle me-2"></i>添加房屋信息</router-link></li>
          <li><router-link to="/houseManage"> <i class="bi bi-circle me-2"></i>管理房屋信息</router-link></li>

        </ul>
      </li>

      <li>
        <a href="#userSubmenu4" @click.prevent="toggleSubmenu('userSubmenu4')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>投诉建议管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu4') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu4') }" id="userSubmenu4">
          <li><router-link to="/complaintsAdd"> <i class="bi bi-circle me-2"></i>提交投诉建议</router-link></li>
          <li><router-link to="/complaintsManage2"> <i class="bi bi-circle me-2"></i>我的投诉建议</router-link></li>

        </ul>
      </li>


      <li>
        <a href="#userSubmenu7" @click.prevent="toggleSubmenu('userSubmenu7')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>费用信息管理</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu7') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu7') }" id="userSubmenu7">
    
          <li><router-link to="/expenseinfoManage2"> <i class="bi bi-circle me-2"></i>我的费用信息</router-link></li>

        </ul>
      </li>

      <li>
        <a href="#userSubmenu26" @click.prevent="toggleSubmenu('userSubmenu26')"
          class="d-flex align-items-center justify-content-between">
          <div>
            <i class="bi bi-people me-2"></i>
            <span>个人中心</span>
          </div>
          <i class="bi bi-chevron-down" :class="{ 'rotate-180': openSubmenus.includes('userSubmenu26') }"></i>
        </a>
        <ul class="collapse list-unstyled" :class="{ show: openSubmenus.includes('userSubmenu26') }" id="userSubmenu26">

                 <li><router-link to="/usersInfo"> <i class="bi bi-circle me-2"></i>修改个人信息</router-link></li>
          <li><router-link to="/password"> <i class="bi bi-circle me-2"></i>修改密码</router-link></li>

        </ul>
      </li>

    </ul>


    <div class="sidebar-footer">
      <a href="javascript:void(0);" @click="exit" class="d-flex align-items-center text-white text-decoration-none p-3">
        <i class="bi bi-box-arrow-left me-2"></i>
        <span>退出登录</span>
      </a>
    </div>
  </nav>
</template>

<script>
export default {
  name: 'LeftMenu',
  data() {
    return {
      userLname: '',
      role: '',
      activeMenuItem: 'dashboard',
      openSubmenus: [],
    };
  },
  mounted() {
    this.userLname = sessionStorage.getItem('userLname');
    this.role = sessionStorage.getItem('role');
  },
  methods: {
    setActiveMenuItem(menuItem) {
      this.activeMenuItem = menuItem;
    },
    toggleSubmenu(submenuId) {
      const index = this.openSubmenus.indexOf(submenuId);
      if (index === -1) {
        // Close other submenus before opening new one
        this.openSubmenus = [submenuId];
      } else {
        this.openSubmenus.splice(index, 1);
      }
    },
    exit() {
      this.$confirm('确认退出吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          sessionStorage.removeItem('userLname');
          sessionStorage.removeItem('role');
          this.$router.push('/');
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.bi-chevron-down {
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

.collapse {
  transition: all 0.3s ease-out;
}

.collapse.show {
  display: block;
}

.sub-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  transition: all 0.3s ease;
}

.sub-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.sub-menu-item i {
  margin-right: 10px;
  font-size: 14px;
}

.left-menu {
  width: 250px;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 999;
  background: #fff;
  transition: all 0.3s;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#content {
  width: calc(100% - 250px);
  margin-left: 250px;
  transition: all 0.3s;
  min-height: 100vh;
}

:global(.sidebar-collapsed) #content {
  margin-left: 0;
  width: 100%;
}

:global(.sidebar-collapsed) .left-menu {
  margin-left: -250px;
}
</style>


