{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue?vue&type=template&id=26084dc2&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue", "mtime": 1749134332807}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "style", "id", "for", "_hoisted_16", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "type", "placeholder", "$data", "loginModel", "username", "$event", "_hoisted_8", "_hoisted_9", "password", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_createVNode", "_component_el_radio", "label", "radio", "onClick", "_cache", "args", "$options", "login", "_hoisted_19", "href", "toreg", "_ctx", "toreg2", "_hoisted_21", "_component_el_dialog", "title", "userRegVisible", "width", "_component_el_form", "model", "userFormData", "ref", "rules", "userRules", "align", "_component_el_form_item", "prop", "_component_el_input", "ulname", "loginpassword", "loginpassword2", "uname", "_component_el_radio_group", "gender", "contact", "_component_el_button", "userReg", "loading", "btnLoading", "staffRegVisible", "staffFormData", "staffRules", "plname", "pword", "pword2", "pname", "age", "address", "staffReg"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <div class=\"container\">\n      <div class=\"login-container\">\n        <div class=\"login-box\">\n          <div class=\"text-center mb-4\">\n            <i class=\"bi bi-shield-lock text-primary\" style=\"font-size: 3rem\"></i>\n            <h2 class=\"mt-3\">小区物业管理系统</h2>\n            <p class=\"text-muted\">请输入您的账号和密码</p>\n          </div>\n          <form id=\"loginForm\">\n            <div class=\"form-floating mb-4\">\n              <input type=\"text\" class=\"form-control\" id=\"username\" placeholder=\"用户名\" v-model=\"loginModel.username\" />\n              <label for=\"username\"><i class=\"bi bi-person me-2\"></i>用户名</label>\n            </div>\n            <div class=\"form-floating mb-4\">\n              <input type=\"password\" class=\"form-control\" id=\"password\" placeholder=\"密码\"\n                v-model=\"loginModel.password\" />\n              <label for=\"password\"><i class=\"bi bi-key me-2\"></i>密码</label>\n            </div>\n            <div class=\"mb-4\">\n              <div class=\"role-selection\">\n                <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\n                <el-radio label=\"用户\" v-model=\"loginModel.radio\">用户</el-radio>\n                <el-radio label=\"工作人员\" v-model=\"loginModel.radio\">工作人员</el-radio>\n\n              </div>\n            </div>\n\n            <button type=\"button\" class=\"btn btn-primary w-100 py-2 mb-3\" @click=\"login\">\n              <i class=\"bi bi-box-arrow-in-right me-2\"></i>登录\n            </button>\n            <div style=\"text-align: center;\">\n              <a href=\"#\" @click=\"toreg\"> 用户注册 </a>&nbsp;&nbsp;|&nbsp;&nbsp;\n\n\n              <a href=\"#\" @click=\"toreg2\"> 工作人员注册 </a>\n            </div>\n\n\n\n          </form>\n\n        </div>\n        <div class=\"text-center text-white mt-4\">\n          <small>&copy; 管理系统. All rights reserved.</small>\n        </div>\n      </div>\n    </div>\n  </div>\n  <el-dialog title=\"用户注册\" v-model=\"userRegVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n    <el-form :model=\"userFormData\" label-width=\"20%\" ref=\"userFormDataRef\" :rules=\"userRules\" align=\"left\">\n      <el-form-item label=\"用户名\" prop=\"ulname\">\n        <el-input v-model=\"userFormData.ulname\" placeholder=\"用户名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"登录密码\" prop=\"loginpassword\">\n        <el-input type=\"password\" v-model=\"userFormData.loginpassword\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码\" prop=\"loginpassword2\">\n        <el-input type=\"password\" v-model=\"userFormData.loginpassword2\" placeholder=\"确认密码\"\n          style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"uname\">\n        <el-input v-model=\"userFormData.uname\" placeholder=\"姓名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"userFormData.gender\">\n          <el-radio label=\"男\">\n            男\n          </el-radio>\n          <el-radio label=\"女\">\n            女\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"userFormData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"userReg\" :loading=\"btnLoading\">注 册</el-button>\n      </el-form-item>\n    </el-form>\n  </el-dialog>\n\n  <el-dialog title=\"工作人员注册\" v-model=\"staffRegVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n    <el-form :model=\"staffFormData\" label-width=\"20%\" ref=\"staffFormDataRef\" :rules=\"staffRules\" align=\"left\">\n      <el-form-item label=\"用户名\" prop=\"plname\">\n        <el-input v-model=\"staffFormData.plname\" placeholder=\"用户名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"登录密码\" prop=\"pword\">\n        <el-input type=\"password\" v-model=\"staffFormData.pword\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码\" prop=\"pword2\">\n        <el-input type=\"password\" v-model=\"staffFormData.pword2\" placeholder=\"确认密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"pname\">\n        <el-input v-model=\"staffFormData.pname\" placeholder=\"姓名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"staffFormData.gender\">\n          <el-radio label=\"男\">\n            男\n          </el-radio>\n          <el-radio label=\"女\">\n            女\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"年龄\" prop=\"age\">\n        <el-input v-model=\"staffFormData.age\" placeholder=\"年龄\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"staffFormData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系地址\" prop=\"address\">\n        <el-input v-model=\"staffFormData.address\" placeholder=\"联系地址\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"staffReg\" :loading=\"btnLoading\">注 册</el-button>\n      </el-form-item>\n    </el-form>\n  </el-dialog>\n</template>\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n      add: true, //是否是添加\n      formVisible: false,\n      formData: {},\n\n      addrules: {\n        ulname: [{ required: true, message: '请输入用户名', trigger: 'blur' },],\n        loginpassword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n        loginpassword2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.loginpassword) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n        uname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n        uflag: [{ required: true, message: '请输入审核状态', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n      add: true, //是否是添加\n      formVisible: false,\n      formData: {},\n\n      addrules: {\n        plname: [{ required: true, message: '请输入用户名', trigger: 'blur' },],\n        pword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n        pword2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.pword) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n        pname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n        age: [{ required: true, message: '请输入年龄', trigger: 'blur' },],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\n        status: [{ required: true, message: '请输入审核状态', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n\n    };\n  },\n  mounted() { },\n  created() {\n\n  },\n  methods: {\n    login() {\n      let that = this;\n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }\n\n      this.loading = true;\n      var role = that.loginModel.radio; //获取身份\n      if (role == '管理员') {\n        let url = base + \"/admin/login\";\n        this.loginModel2.aname = this.loginModel.username;\n        this.loginModel2.password = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.aname);\n            sessionStorage.setItem(\"role\", \"管理员\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n      else if (role == '工作人员') {\n        let url = base + \"/propertystaff/login\";\n        this.loginModel2.plname = this.loginModel.username;\n        this.loginModel2.pword = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.plname);\n            sessionStorage.setItem(\"role\", \"工作人员\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n      else if (role == '用户') {\n        let url = base + \"/users/login\";\n        this.loginModel2.ulname = this.loginModel.username;\n        this.loginModel2.loginpassword = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.ulname);\n            sessionStorage.setItem(\"role\", \"用户\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n\n\n    },\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/users/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          this.formData.uflag = \"待审核\";\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/users/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          this.formData.status = \"待审核\";\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/propertystaff/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请登录！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n\n\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import url(../assets/css/ht_bootstrap.min.css);\n@import url(../assets/css/ht_bootstrap-icons.css);\n@import url(../assets/css/ht_style.css);\n\n.role-selection {\n  display: flex;\n  justify-content: center;\n  gap: 30px;\n  padding: 10px 0;\n}\n\n.role-option {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n}\n\n.role-option input[type='radio'] {\n  width: 18px;\n  height: 18px;\n  margin: 0;\n  cursor: pointer;\n}\n\n.role-option label {\n  margin: 0;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #6e707e;\n}\n\n.role-option:hover label {\n  color: var(--primary-color);\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;gEACpBC,mBAAA,CAIM;EAJDD,KAAK,EAAC;AAAkB,I,aAC3BC,mBAAA,CAAsE;EAAnED,KAAK,EAAC,gCAAgC;EAACE,KAAuB,EAAvB;IAAA;EAAA;iBAC1CD,mBAAA,CAA8B;EAA1BD,KAAK,EAAC;AAAM,GAAC,UAAQ,G,aACzBC,mBAAA,CAAoC;EAAjCD,KAAK,EAAC;AAAY,GAAC,YAAU,E;;EAE5BG,EAAE,EAAC;AAAW;;EACbH,KAAK,EAAC;AAAoB;gEAE7BC,mBAAA,CAAkE;EAA3DG,GAAG,EAAC;AAAU,I,aAACH,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,I,8BAAK,KAAG,E;;EAEvDA,KAAK,EAAC;AAAoB;iEAG7BC,mBAAA,CAA8D;EAAvDG,GAAG,EAAC;AAAU,I,aAACH,mBAAA,CAA8B;EAA3BD,KAAK,EAAC;AAAgB,I,8BAAK,IAAE,E;;EAEnDA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgB;kDACwB,KAAG;kDACJ,IAAE;kDACA,MAAI;iEAMxDC,mBAAA,CAA6C;EAA1CD,KAAK,EAAC;AAA+B;kDAAK,KAC/C;qBADEK,WAA6C,E;;EAE1CH,KAA2B,EAA3B;IAAA;EAAA;AAA2B;kDACO,QAGrC;iEAQND,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAA6B,I,aACtCC,mBAAA,CAAgD,eAAzC,8BAAiC,E;kDAsBpB,KAEpB;kDACoB,KAEpB;kDAQ+D,KAAG;kDAqB9C,KAEpB;kDACoB,KAEpB;kDAcgE,KAAG;;;;;;;;;6DAvH3EA,mBAAA,CAgDM,OAhDNK,UAgDM,GA/CJL,mBAAA,CA8CM,OA9CNM,UA8CM,GA7CJN,mBAAA,CA4CM,OA5CNO,UA4CM,GA3CJP,mBAAA,CAuCM,OAvCNQ,UAuCM,GAtCJC,UAIM,EACNT,mBAAA,CA+BO,QA/BPU,UA+BO,GA9BLV,mBAAA,CAGM,OAHNW,UAGM,G,gBAFJX,mBAAA,CAAwG;IAAjGY,IAAI,EAAC,MAAM;IAACb,KAAK,EAAC,cAAc;IAACG,EAAE,EAAC,UAAU;IAACW,WAAW,EAAC,KAAK;+DAAUC,KAAA,CAAAC,UAAU,CAACC,QAAQ,GAAAC,MAAA;iDAAnBH,KAAA,CAAAC,UAAU,CAACC,QAAQ,E,GACpGE,UAAkE,C,GAEpElB,mBAAA,CAIM,OAJNmB,UAIM,G,gBAHJnB,mBAAA,CACkC;IAD3BY,IAAI,EAAC,UAAU;IAACb,KAAK,EAAC,cAAc;IAACG,EAAE,EAAC,UAAU;IAACW,WAAW,EAAC,IAAI;+DAC/DC,KAAA,CAAAC,UAAU,CAACK,QAAQ,GAAAH,MAAA;iDAAnBH,KAAA,CAAAC,UAAU,CAACK,QAAQ,E,GAC9BC,WAA8D,C,GAEhErB,mBAAA,CAOM,OAPNsB,WAOM,GANJtB,mBAAA,CAKM,OALNuB,WAKM,GAJJC,YAAA,CAA+DC,mBAAA;IAArDC,KAAK,EAAC,KAAK;gBAAUZ,KAAA,CAAAC,UAAU,CAACY,KAAK;+DAAhBb,KAAA,CAAAC,UAAU,CAACY,KAAK,GAAAV,MAAA;;sBAAE,MAAG,C;;qCACpDO,YAAA,CAA6DC,mBAAA;IAAnDC,KAAK,EAAC,IAAI;gBAAUZ,KAAA,CAAAC,UAAU,CAACY,KAAK;+DAAhBb,KAAA,CAAAC,UAAU,CAACY,KAAK,GAAAV,MAAA;;sBAAE,MAAE,C;;qCAClDO,YAAA,CAAiEC,mBAAA;IAAvDC,KAAK,EAAC,MAAM;gBAAUZ,KAAA,CAAAC,UAAU,CAACY,KAAK;+DAAhBb,KAAA,CAAAC,UAAU,CAACY,KAAK,GAAAV,MAAA;;sBAAE,MAAI,C;;yCAK1DjB,mBAAA,CAES;IAFDY,IAAI,EAAC,QAAQ;IAACb,KAAK,EAAC,iCAAiC;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,IAAAF,IAAA,CAAK;mBAG3E9B,mBAAA,CAKM,OALNiC,WAKM,GAJJjC,mBAAA,CAAqC;IAAlCkC,IAAI,EAAC,GAAG;IAAEN,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAI,KAAA,IAAAJ,QAAA,CAAAI,KAAA,IAAAL,IAAA,CAAK;KAAE,QAAM,G,aAGjC9B,mBAAA,CAAwC;IAArCkC,IAAI,EAAC,GAAG;IAAEN,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEM,IAAA,CAAAC,MAAA,IAAAD,IAAA,CAAAC,MAAA,IAAAP,IAAA,CAAM;KAAE,UAAQ,E,OAQ1CQ,WAEM,C,OAIZd,YAAA,CAiCYe,oBAAA;IAjCDC,KAAK,EAAC,MAAM;gBAAUJ,IAAA,CAAAK,cAAc;iEAAdL,IAAA,CAAAK,cAAc,GAAAxB,MAAA;IAAEyB,KAAK,EAAC,KAAK;IAAE,sBAAoB,EAAE;;sBAClF,MA+BU,CA/BVlB,YAAA,CA+BUmB,kBAAA;MA/BAC,KAAK,EAAER,IAAA,CAAAS,YAAY;MAAE,aAAW,EAAC,KAAK;MAACC,GAAG,EAAC,iBAAiB;MAAEC,KAAK,EAAEX,IAAA,CAAAY,SAAS;MAAEC,KAAK,EAAC;;wBAC9F,MAEe,CAFfzB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,KAAK;QAACyB,IAAI,EAAC;;0BAC7B,MAAwF,CAAxF3B,YAAA,CAAwF4B,mBAAA;sBAArEhB,IAAA,CAAAS,YAAY,CAACQ,MAAM;qEAAnBjB,IAAA,CAAAS,YAAY,CAACQ,MAAM,GAAApC,MAAA;UAAEJ,WAAW,EAAC,KAAK;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAE5DuB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MAAgH,CAAhH3B,YAAA,CAAgH4B,mBAAA;UAAtGxC,IAAI,EAAC,UAAU;sBAAUwB,IAAA,CAAAS,YAAY,CAACS,aAAa;qEAA1BlB,IAAA,CAAAS,YAAY,CAACS,aAAa,GAAArC,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAEpFuB,YAAA,CAGe0B,uBAAA;QAHDxB,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MACgC,CADhC3B,YAAA,CACgC4B,mBAAA;UADtBxC,IAAI,EAAC,UAAU;sBAAUwB,IAAA,CAAAS,YAAY,CAACU,cAAc;uEAA3BnB,IAAA,CAAAS,YAAY,CAACU,cAAc,GAAAtC,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAChFZ,KAAkB,EAAlB;YAAA;UAAA;;;UAEJuB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,IAAI;QAACyB,IAAI,EAAC;;0BAC5B,MAAsF,CAAtF3B,YAAA,CAAsF4B,mBAAA;sBAAnEhB,IAAA,CAAAS,YAAY,CAACW,KAAK;uEAAlBpB,IAAA,CAAAS,YAAY,CAACW,KAAK,GAAAvC,MAAA;UAAEJ,WAAW,EAAC,IAAI;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAE1DuB,YAAA,CASe0B,uBAAA;QATDxB,KAAK,EAAC,IAAI;QAACyB,IAAI,EAAC;;0BAC5B,MAOiB,CAPjB3B,YAAA,CAOiBiC,yBAAA;sBAPQrB,IAAA,CAAAS,YAAY,CAACa,MAAM;uEAAnBtB,IAAA,CAAAS,YAAY,CAACa,MAAM,GAAAzC,MAAA;;4BAC1C,MAEW,CAFXO,YAAA,CAEWC,mBAAA;YAFDC,KAAK,EAAC;UAAG;8BAAC,MAEpB,C;;cACAF,YAAA,CAEWC,mBAAA;YAFDC,KAAK,EAAC;UAAG;8BAAC,MAEpB,C;;;;;;;UAGJF,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MAA0F,CAA1F3B,YAAA,CAA0F4B,mBAAA;sBAAvEhB,IAAA,CAAAS,YAAY,CAACc,OAAO;uEAApBvB,IAAA,CAAAS,YAAY,CAACc,OAAO,GAAA1C,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAG9DuB,YAAA,CAEe0B,uBAAA;0BADb,MAAgF,CAAhF1B,YAAA,CAAgFoC,oBAAA;UAArEhD,IAAI,EAAC,SAAS;UAAEgB,OAAK,EAAEQ,IAAA,CAAAyB,OAAO;UAAGC,OAAO,EAAEhD,KAAA,CAAAiD;;4BAAY,MAAG,C;;;;;;;;;qCAK1EvC,YAAA,CAsCYe,oBAAA;IAtCDC,KAAK,EAAC,QAAQ;gBAAUJ,IAAA,CAAA4B,eAAe;iEAAf5B,IAAA,CAAA4B,eAAe,GAAA/C,MAAA;IAAEyB,KAAK,EAAC,KAAK;IAAE,sBAAoB,EAAE;;sBACrF,MAoCU,CApCVlB,YAAA,CAoCUmB,kBAAA;MApCAC,KAAK,EAAER,IAAA,CAAA6B,aAAa;MAAE,aAAW,EAAC,KAAK;MAACnB,GAAG,EAAC,kBAAkB;MAAEC,KAAK,EAAEX,IAAA,CAAA8B,UAAU;MAAEjB,KAAK,EAAC;;wBACjG,MAEe,CAFfzB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,KAAK;QAACyB,IAAI,EAAC;;0BAC7B,MAAyF,CAAzF3B,YAAA,CAAyF4B,mBAAA;sBAAtEhB,IAAA,CAAA6B,aAAa,CAACE,MAAM;uEAApB/B,IAAA,CAAA6B,aAAa,CAACE,MAAM,GAAAlD,MAAA;UAAEJ,WAAW,EAAC,KAAK;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAE7DuB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MAAyG,CAAzG3B,YAAA,CAAyG4B,mBAAA;UAA/FxC,IAAI,EAAC,UAAU;sBAAUwB,IAAA,CAAA6B,aAAa,CAACG,KAAK;uEAAnBhC,IAAA,CAAA6B,aAAa,CAACG,KAAK,GAAAnD,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAE7EuB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MAA0G,CAA1G3B,YAAA,CAA0G4B,mBAAA;UAAhGxC,IAAI,EAAC,UAAU;sBAAUwB,IAAA,CAAA6B,aAAa,CAACI,MAAM;uEAApBjC,IAAA,CAAA6B,aAAa,CAACI,MAAM,GAAApD,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAE9EuB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,IAAI;QAACyB,IAAI,EAAC;;0BAC5B,MAAuF,CAAvF3B,YAAA,CAAuF4B,mBAAA;sBAApEhB,IAAA,CAAA6B,aAAa,CAACK,KAAK;uEAAnBlC,IAAA,CAAA6B,aAAa,CAACK,KAAK,GAAArD,MAAA;UAAEJ,WAAW,EAAC,IAAI;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAE3DuB,YAAA,CASe0B,uBAAA;QATDxB,KAAK,EAAC,IAAI;QAACyB,IAAI,EAAC;;0BAC5B,MAOiB,CAPjB3B,YAAA,CAOiBiC,yBAAA;sBAPQrB,IAAA,CAAA6B,aAAa,CAACP,MAAM;uEAApBtB,IAAA,CAAA6B,aAAa,CAACP,MAAM,GAAAzC,MAAA;;4BAC3C,MAEW,CAFXO,YAAA,CAEWC,mBAAA;YAFDC,KAAK,EAAC;UAAG;8BAAC,MAEpB,C;;cACAF,YAAA,CAEWC,mBAAA;YAFDC,KAAK,EAAC;UAAG;8BAAC,MAEpB,C;;;;;;;UAGJF,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,IAAI;QAACyB,IAAI,EAAC;;0BAC5B,MAAqF,CAArF3B,YAAA,CAAqF4B,mBAAA;sBAAlEhB,IAAA,CAAA6B,aAAa,CAACM,GAAG;uEAAjBnC,IAAA,CAAA6B,aAAa,CAACM,GAAG,GAAAtD,MAAA;UAAEJ,WAAW,EAAC,IAAI;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAEzDuB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MAA2F,CAA3F3B,YAAA,CAA2F4B,mBAAA;sBAAxEhB,IAAA,CAAA6B,aAAa,CAACN,OAAO;uEAArBvB,IAAA,CAAA6B,aAAa,CAACN,OAAO,GAAA1C,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAE/DuB,YAAA,CAEe0B,uBAAA;QAFDxB,KAAK,EAAC,MAAM;QAACyB,IAAI,EAAC;;0BAC9B,MAA2F,CAA3F3B,YAAA,CAA2F4B,mBAAA;sBAAxEhB,IAAA,CAAA6B,aAAa,CAACO,OAAO;uEAArBpC,IAAA,CAAA6B,aAAa,CAACO,OAAO,GAAAvD,MAAA;UAAEJ,WAAW,EAAC,MAAM;UAACZ,KAAkB,EAAlB;YAAA;UAAA;;;UAG/DuB,YAAA,CAEe0B,uBAAA;0BADb,MAAiF,CAAjF1B,YAAA,CAAiFoC,oBAAA;UAAtEhD,IAAI,EAAC,SAAS;UAAEgB,OAAK,EAAEQ,IAAA,CAAAqC,QAAQ;UAAGX,OAAO,EAAEhD,KAAA,CAAAiD;;4BAAY,MAAG,C"}]}