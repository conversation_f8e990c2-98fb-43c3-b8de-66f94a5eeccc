{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue", "mtime": 1749134332807}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "year", "Date", "getFullYear", "loginModel", "username", "password", "radio", "loginModel2", "add", "formVisible", "formData", "add<PERSON><PERSON>", "ulname", "required", "message", "trigger", "loginpassword", "loginpassword2", "validator", "rule", "value", "callback", "Error", "uname", "gender", "contact", "uflag", "btnLoading", "plname", "pword", "pword2", "pname", "age", "address", "status", "mounted", "created", "methods", "login", "that", "$message", "type", "loading", "role", "url", "aname", "post", "then", "res", "code", "console", "log", "JSON", "stringify", "resdata", "sessionStorage", "setItem", "$router", "push", "msg", "toreg", "isClear", "rules", "$nextTick", "$refs", "resetFields", "reg", "validate", "valid", "offset", "clearValidate"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <div class=\"container\">\n      <div class=\"login-container\">\n        <div class=\"login-box\">\n          <div class=\"text-center mb-4\">\n            <i class=\"bi bi-shield-lock text-primary\" style=\"font-size: 3rem\"></i>\n            <h2 class=\"mt-3\">小区物业管理系统</h2>\n            <p class=\"text-muted\">请输入您的账号和密码</p>\n          </div>\n          <form id=\"loginForm\">\n            <div class=\"form-floating mb-4\">\n              <input type=\"text\" class=\"form-control\" id=\"username\" placeholder=\"用户名\" v-model=\"loginModel.username\" />\n              <label for=\"username\"><i class=\"bi bi-person me-2\"></i>用户名</label>\n            </div>\n            <div class=\"form-floating mb-4\">\n              <input type=\"password\" class=\"form-control\" id=\"password\" placeholder=\"密码\"\n                v-model=\"loginModel.password\" />\n              <label for=\"password\"><i class=\"bi bi-key me-2\"></i>密码</label>\n            </div>\n            <div class=\"mb-4\">\n              <div class=\"role-selection\">\n                <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\n                <el-radio label=\"用户\" v-model=\"loginModel.radio\">用户</el-radio>\n                <el-radio label=\"工作人员\" v-model=\"loginModel.radio\">工作人员</el-radio>\n\n              </div>\n            </div>\n\n            <button type=\"button\" class=\"btn btn-primary w-100 py-2 mb-3\" @click=\"login\">\n              <i class=\"bi bi-box-arrow-in-right me-2\"></i>登录\n            </button>\n            <div style=\"text-align: center;\">\n              <a href=\"#\" @click=\"toreg\"> 用户注册 </a>&nbsp;&nbsp;|&nbsp;&nbsp;\n\n\n              <a href=\"#\" @click=\"toreg2\"> 工作人员注册 </a>\n            </div>\n\n\n\n          </form>\n\n        </div>\n        <div class=\"text-center text-white mt-4\">\n          <small>&copy; 管理系统. All rights reserved.</small>\n        </div>\n      </div>\n    </div>\n  </div>\n  <el-dialog title=\"用户注册\" v-model=\"userRegVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n    <el-form :model=\"userFormData\" label-width=\"20%\" ref=\"userFormDataRef\" :rules=\"userRules\" align=\"left\">\n      <el-form-item label=\"用户名\" prop=\"ulname\">\n        <el-input v-model=\"userFormData.ulname\" placeholder=\"用户名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"登录密码\" prop=\"loginpassword\">\n        <el-input type=\"password\" v-model=\"userFormData.loginpassword\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码\" prop=\"loginpassword2\">\n        <el-input type=\"password\" v-model=\"userFormData.loginpassword2\" placeholder=\"确认密码\"\n          style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"uname\">\n        <el-input v-model=\"userFormData.uname\" placeholder=\"姓名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"userFormData.gender\">\n          <el-radio label=\"男\">\n            男\n          </el-radio>\n          <el-radio label=\"女\">\n            女\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"userFormData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"userReg\" :loading=\"btnLoading\">注 册</el-button>\n      </el-form-item>\n    </el-form>\n  </el-dialog>\n\n  <el-dialog title=\"工作人员注册\" v-model=\"staffRegVisible\" width=\"40%\" :close-on-click-modal=\"false\">\n    <el-form :model=\"staffFormData\" label-width=\"20%\" ref=\"staffFormDataRef\" :rules=\"staffRules\" align=\"left\">\n      <el-form-item label=\"用户名\" prop=\"plname\">\n        <el-input v-model=\"staffFormData.plname\" placeholder=\"用户名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"登录密码\" prop=\"pword\">\n        <el-input type=\"password\" v-model=\"staffFormData.pword\" placeholder=\"登录密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"确认密码\" prop=\"pword2\">\n        <el-input type=\"password\" v-model=\"staffFormData.pword2\" placeholder=\"确认密码\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"姓名\" prop=\"pname\">\n        <el-input v-model=\"staffFormData.pname\" placeholder=\"姓名\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"性别\" prop=\"gender\">\n        <el-radio-group v-model=\"staffFormData.gender\">\n          <el-radio label=\"男\">\n            男\n          </el-radio>\n          <el-radio label=\"女\">\n            女\n          </el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"年龄\" prop=\"age\">\n        <el-input v-model=\"staffFormData.age\" placeholder=\"年龄\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系方式\" prop=\"contact\">\n        <el-input v-model=\"staffFormData.contact\" placeholder=\"联系方式\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"联系地址\" prop=\"address\">\n        <el-input v-model=\"staffFormData.address\" placeholder=\"联系地址\" style=\"width:50%;\"></el-input>\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"staffReg\" :loading=\"btnLoading\">注 册</el-button>\n      </el-form-item>\n    </el-form>\n  </el-dialog>\n</template>\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n      add: true, //是否是添加\n      formVisible: false,\n      formData: {},\n\n      addrules: {\n        ulname: [{ required: true, message: '请输入用户名', trigger: 'blur' },],\n        loginpassword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n        loginpassword2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.loginpassword) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n        uname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n        uflag: [{ required: true, message: '请输入审核状态', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n      add: true, //是否是添加\n      formVisible: false,\n      formData: {},\n\n      addrules: {\n        plname: [{ required: true, message: '请输入用户名', trigger: 'blur' },],\n        pword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],\n        pword2: [{ required: true, message: '请输入登录密码', trigger: 'blur' }, { validator: (rule, value, callback) => { if (value !== this.formData.pword) { callback(new Error('两次输入密码不一致!')); } else { callback(); } }, trigger: 'blur' },],\n        pname: [{ required: true, message: '请输入姓名', trigger: 'blur' },],\n        gender: [{ required: true, message: '请输入性别', trigger: 'blur' },],\n        age: [{ required: true, message: '请输入年龄', trigger: 'blur' },],\n        contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' },],\n        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' },],\n        status: [{ required: true, message: '请输入审核状态', trigger: 'blur' },],\n      },\n\n\n      btnLoading: false, //按钮是否在加载中\n\n\n    };\n  },\n  mounted() { },\n  created() {\n\n  },\n  methods: {\n    login() {\n      let that = this;\n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }\n\n      this.loading = true;\n      var role = that.loginModel.radio; //获取身份\n      if (role == '管理员') {\n        let url = base + \"/admin/login\";\n        this.loginModel2.aname = this.loginModel.username;\n        this.loginModel2.password = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.aname);\n            sessionStorage.setItem(\"role\", \"管理员\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n      else if (role == '工作人员') {\n        let url = base + \"/propertystaff/login\";\n        this.loginModel2.plname = this.loginModel.username;\n        this.loginModel2.pword = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.plname);\n            sessionStorage.setItem(\"role\", \"工作人员\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n      else if (role == '用户') {\n        let url = base + \"/users/login\";\n        this.loginModel2.ulname = this.loginModel.username;\n        this.loginModel2.loginpassword = this.loginModel.password;\n        request.post(url, this.loginModel2).then((res) => {\n          this.loading = false;\n          if (res.code == 200) {\n            console.log(JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n            sessionStorage.setItem(\"userLname\", res.resdata.ulname);\n            sessionStorage.setItem(\"role\", \"用户\");\n            this.$router.push(\"/main\");\n          } else {\n            this.$message({\n              message: res.msg,\n              type: \"error\",\n            });\n          }\n        });\n      }\n\n\n    },\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/users/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          this.formData.uflag = \"待审核\";\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/users/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          this.formData.status = \"待审核\";\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请等待管理员的审核！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n    toreg() {\n      this.formVisible = true;\n      this.add = true;\n      this.isClear = true;\n      this.rules = this.addrules;\n      this.$nextTick(() => {\n        this.$refs[\"formDataRef\"].resetFields();\n      });\n    },\n\n    //注册\n    reg() {\n      //表单验证\n      this.$refs[\"formDataRef\"].validate((valid) => {\n\n        if (valid) {\n          let url = base + \"/propertystaff/add\"; //请求地址\n          this.btnLoading = true; //按钮加载状态\n          request.post(url, this.formData).then((res) => { //请求接口             \n            if (res.code == 200) {\n              this.$message({\n                message: \"恭喜您，注册成功，请登录！\",\n                type: \"success\",\n                offset: 320,\n              });\n              this.formVisible = false; //关闭表单\n              this.btnLoading = false; //按钮加载状态\n              this.$refs[\"formDataRef\"].resetFields(); //重置表单\n              this.$refs[\"formDataRef\"].clearValidate();\n            }\n            else if (res.code == 201) {\n              this.$message({\n                message: res.msg,\n                type: \"error\",\n                offset: 320,\n              });\n            }\n            else {\n              this.$message({\n                message: \"服务器错误\",\n                type: \"error\",\n                offset: 320,\n              });\n            }\n          });\n        }\n      });\n    },\n\n\n\n\n\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import url(../assets/css/ht_bootstrap.min.css);\n@import url(../assets/css/ht_bootstrap-icons.css);\n@import url(../assets/css/ht_style.css);\n\n.role-selection {\n  display: flex;\n  justify-content: center;\n  gap: 30px;\n  padding: 10px 0;\n}\n\n.role-option {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n}\n\n.role-option input[type='radio'] {\n  width: 18px;\n  height: 18px;\n  margin: 0;\n  cursor: pointer;\n}\n\n.role-option label {\n  margin: 0;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #6e707e;\n}\n\n.role-option:hover label {\n  color: var(--primary-color);\n}\n</style>\n"], "mappings": ";;AA8HA,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAChD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9BC,UAAU,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC;MACDC,WAAW,EAAE,CAAC,CAAC;MACfC,GAAG,EAAE,IAAI;MAAE;MACXC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE,CAAC,CAAC;MAEZC,QAAQ,EAAE;QACRC,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACjEC,aAAa,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACxEE,cAAc,EAAE,CAAC;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EAAE;UAAEG,SAAS,EAAEA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;YAAE,IAAID,KAAI,KAAM,IAAI,CAACV,QAAQ,CAACM,aAAa,EAAE;cAAEK,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;YAAE,OAAO;cAAED,QAAQ,CAAC,CAAC;YAAE;UAAE,CAAC;UAAEN,OAAO,EAAE;QAAO,CAAC,CAAE;QACjPQ,KAAK,EAAE,CAAC;UAAEV,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAC/DS,MAAM,EAAE,CAAC;UAAEX,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAChEU,OAAO,EAAE,CAAC;UAAEZ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnEW,KAAK,EAAE,CAAC;UAAEb,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MACjE,CAAC;MAGDY,UAAU,EAAE,KAAK;MAAE;;MAEnBnB,GAAG,EAAE,IAAI;MAAE;MACXC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE,CAAC,CAAC;MAEZC,QAAQ,EAAE;QACRiB,MAAM,EAAE,CAAC;UAAEf,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACjEc,KAAK,EAAE,CAAC;UAAEhB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAChEe,MAAM,EAAE,CAAC;UAAEjB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EAAE;UAAEG,SAAS,EAAEA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;YAAE,IAAID,KAAI,KAAM,IAAI,CAACV,QAAQ,CAACmB,KAAK,EAAE;cAAER,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;YAAE,OAAO;cAAED,QAAQ,CAAC,CAAC;YAAE;UAAE,CAAC;UAAEN,OAAO,EAAE;QAAO,CAAC,CAAE;QACjOgB,KAAK,EAAE,CAAC;UAAElB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAC/DS,MAAM,EAAE,CAAC;UAAEX,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAChEiB,GAAG,EAAE,CAAC;UAAEnB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QAC7DU,OAAO,EAAE,CAAC;UAAEZ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnEkB,OAAO,EAAE,CAAC;UAAEpB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAE;QACnEmB,MAAM,EAAE,CAAC;UAAErB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAClE,CAAC;MAGDY,UAAU,EAAE,KAAK,CAAE;IAGrB,CAAC;EACH,CAAC;;EACDQ,OAAOA,CAAA,EAAG,CAAE,CAAC;EACbC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE;IACPC,KAAKA,CAAA,EAAG;MACN,IAAIC,IAAG,GAAI,IAAI;MAEf,IAAIA,IAAI,CAACpC,UAAU,CAACC,QAAO,IAAK,EAAE,EAAE;QAClCmC,IAAI,CAACC,QAAQ,CAAC;UACZ1B,OAAO,EAAE,OAAO;UAChB2B,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;MACA,IAAIF,IAAI,CAACpC,UAAU,CAACE,QAAO,IAAK,EAAE,EAAE;QAClCkC,IAAI,CAACC,QAAQ,CAAC;UACZ1B,OAAO,EAAE,OAAO;UAChB2B,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACC,OAAM,GAAI,IAAI;MACnB,IAAIC,IAAG,GAAIJ,IAAI,CAACpC,UAAU,CAACG,KAAK,EAAE;MAClC,IAAIqC,IAAG,IAAK,KAAK,EAAE;QACjB,IAAIC,GAAE,GAAI/C,IAAG,GAAI,cAAc;QAC/B,IAAI,CAACU,WAAW,CAACsC,KAAI,GAAI,IAAI,CAAC1C,UAAU,CAACC,QAAQ;QACjD,IAAI,CAACG,WAAW,CAACF,QAAO,GAAI,IAAI,CAACF,UAAU,CAACE,QAAQ;QACpDT,OAAO,CAACkD,IAAI,CAACF,GAAG,EAAE,IAAI,CAACrC,WAAW,CAAC,CAACwC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACN,OAAM,GAAI,KAAK;UACpB,IAAIM,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAACT,KAAK,CAAC;YACtDU,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;YACrC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAAClB,QAAQ,CAAC;cACZ1B,OAAO,EAAEkC,GAAG,CAACW,GAAG;cAChBlB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,OACK,IAAIE,IAAG,IAAK,MAAM,EAAE;QACvB,IAAIC,GAAE,GAAI/C,IAAG,GAAI,sBAAsB;QACvC,IAAI,CAACU,WAAW,CAACqB,MAAK,GAAI,IAAI,CAACzB,UAAU,CAACC,QAAQ;QAClD,IAAI,CAACG,WAAW,CAACsB,KAAI,GAAI,IAAI,CAAC1B,UAAU,CAACE,QAAQ;QACjDT,OAAO,CAACkD,IAAI,CAACF,GAAG,EAAE,IAAI,CAACrC,WAAW,CAAC,CAACwC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACN,OAAM,GAAI,KAAK;UACpB,IAAIM,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAAC1B,MAAM,CAAC;YACvD2B,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;YACtC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAAClB,QAAQ,CAAC;cACZ1B,OAAO,EAAEkC,GAAG,CAACW,GAAG;cAChBlB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,OACK,IAAIE,IAAG,IAAK,IAAI,EAAE;QACrB,IAAIC,GAAE,GAAI/C,IAAG,GAAI,cAAc;QAC/B,IAAI,CAACU,WAAW,CAACK,MAAK,GAAI,IAAI,CAACT,UAAU,CAACC,QAAQ;QAClD,IAAI,CAACG,WAAW,CAACS,aAAY,GAAI,IAAI,CAACb,UAAU,CAACE,QAAQ;QACzDT,OAAO,CAACkD,IAAI,CAACF,GAAG,EAAE,IAAI,CAACrC,WAAW,CAAC,CAACwC,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACN,OAAM,GAAI,KAAK;UACpB,IAAIM,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAAC1C,MAAM,CAAC;YACvD2C,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;YACpC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAAClB,QAAQ,CAAC;cACZ1B,OAAO,EAAEkC,GAAG,CAACW,GAAG;cAChBlB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IAGF,CAAC;IAEDmB,KAAKA,CAAA,EAAG;MACN,IAAI,CAACnD,WAAU,GAAI,IAAI;MACvB,IAAI,CAACD,GAAE,GAAI,IAAI;MACf,IAAI,CAACqD,OAAM,GAAI,IAAI;MACnB,IAAI,CAACC,KAAI,GAAI,IAAI,CAACnD,QAAQ;MAC1B,IAAI,CAACoD,SAAS,CAAC,MAAM;QACnB,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,GAAGA,CAAA,EAAG;MACJ;MACA,IAAI,CAACF,KAAK,CAAC,aAAa,CAAC,CAACG,QAAQ,CAAEC,KAAK,IAAK;QAE5C,IAAIA,KAAK,EAAE;UACT,IAAIxB,GAAE,GAAI/C,IAAG,GAAI,YAAY,EAAE;UAC/B,IAAI,CAAC8B,UAAS,GAAI,IAAI,EAAE;UACxB,IAAI,CAACjB,QAAQ,CAACgB,KAAI,GAAI,KAAK;UAC3B9B,OAAO,CAACkD,IAAI,CAACF,GAAG,EAAE,IAAI,CAAClC,QAAQ,CAAC,CAACqC,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACT,QAAQ,CAAC;gBACZ1B,OAAO,EAAE,qBAAqB;gBAC9B2B,IAAI,EAAE,SAAS;gBACf4B,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAAC5D,WAAU,GAAI,KAAK,EAAE;cAC1B,IAAI,CAACkB,UAAS,GAAI,KAAK,EAAE;cACzB,IAAI,CAACqC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;cACzC,IAAI,CAACD,KAAK,CAAC,aAAa,CAAC,CAACM,aAAa,CAAC,CAAC;YAC3C,OACK,IAAItB,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACxB,IAAI,CAACT,QAAQ,CAAC;gBACZ1B,OAAO,EAAEkC,GAAG,CAACW,GAAG;gBAChBlB,IAAI,EAAE,OAAO;gBACb4B,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,OACK;cACH,IAAI,CAAC7B,QAAQ,CAAC;gBACZ1B,OAAO,EAAE,OAAO;gBAChB2B,IAAI,EAAE,OAAO;gBACb4B,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAKDT,KAAKA,CAAA,EAAG;MACN,IAAI,CAACnD,WAAU,GAAI,IAAI;MACvB,IAAI,CAACD,GAAE,GAAI,IAAI;MACf,IAAI,CAACqD,OAAM,GAAI,IAAI;MACnB,IAAI,CAACC,KAAI,GAAI,IAAI,CAACnD,QAAQ;MAC1B,IAAI,CAACoD,SAAS,CAAC,MAAM;QACnB,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,GAAGA,CAAA,EAAG;MACJ;MACA,IAAI,CAACF,KAAK,CAAC,aAAa,CAAC,CAACG,QAAQ,CAAEC,KAAK,IAAK;QAE5C,IAAIA,KAAK,EAAE;UACT,IAAIxB,GAAE,GAAI/C,IAAG,GAAI,YAAY,EAAE;UAC/B,IAAI,CAAC8B,UAAS,GAAI,IAAI,EAAE;UACxB,IAAI,CAACjB,QAAQ,CAACwB,MAAK,GAAI,KAAK;UAC5BtC,OAAO,CAACkD,IAAI,CAACF,GAAG,EAAE,IAAI,CAAClC,QAAQ,CAAC,CAACqC,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACT,QAAQ,CAAC;gBACZ1B,OAAO,EAAE,qBAAqB;gBAC9B2B,IAAI,EAAE,SAAS;gBACf4B,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAAC5D,WAAU,GAAI,KAAK,EAAE;cAC1B,IAAI,CAACkB,UAAS,GAAI,KAAK,EAAE;cACzB,IAAI,CAACqC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;cACzC,IAAI,CAACD,KAAK,CAAC,aAAa,CAAC,CAACM,aAAa,CAAC,CAAC;YAC3C,OACK,IAAItB,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACxB,IAAI,CAACT,QAAQ,CAAC;gBACZ1B,OAAO,EAAEkC,GAAG,CAACW,GAAG;gBAChBlB,IAAI,EAAE,OAAO;gBACb4B,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,OACK;cACH,IAAI,CAAC7B,QAAQ,CAAC;gBACZ1B,OAAO,EAAE,OAAO;gBAChB2B,IAAI,EAAE,OAAO;gBACb4B,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAIDT,KAAKA,CAAA,EAAG;MACN,IAAI,CAACnD,WAAU,GAAI,IAAI;MACvB,IAAI,CAACD,GAAE,GAAI,IAAI;MACf,IAAI,CAACqD,OAAM,GAAI,IAAI;MACnB,IAAI,CAACC,KAAI,GAAI,IAAI,CAACnD,QAAQ;MAC1B,IAAI,CAACoD,SAAS,CAAC,MAAM;QACnB,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,GAAGA,CAAA,EAAG;MACJ;MACA,IAAI,CAACF,KAAK,CAAC,aAAa,CAAC,CAACG,QAAQ,CAAEC,KAAK,IAAK;QAE5C,IAAIA,KAAK,EAAE;UACT,IAAIxB,GAAE,GAAI/C,IAAG,GAAI,oBAAoB,EAAE;UACvC,IAAI,CAAC8B,UAAS,GAAI,IAAI,EAAE;UACxB/B,OAAO,CAACkD,IAAI,CAACF,GAAG,EAAE,IAAI,CAAClC,QAAQ,CAAC,CAACqC,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACT,QAAQ,CAAC;gBACZ1B,OAAO,EAAE,eAAe;gBACxB2B,IAAI,EAAE,SAAS;gBACf4B,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAAC5D,WAAU,GAAI,KAAK,EAAE;cAC1B,IAAI,CAACkB,UAAS,GAAI,KAAK,EAAE;cACzB,IAAI,CAACqC,KAAK,CAAC,aAAa,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;cACzC,IAAI,CAACD,KAAK,CAAC,aAAa,CAAC,CAACM,aAAa,CAAC,CAAC;YAC3C,OACK,IAAItB,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACxB,IAAI,CAACT,QAAQ,CAAC;gBACZ1B,OAAO,EAAEkC,GAAG,CAACW,GAAG;gBAChBlB,IAAI,EAAE,OAAO;gBACb4B,MAAM,EAAE;cACV,CAAC,CAAC;YACJ,OACK;cACH,IAAI,CAAC7B,QAAQ,CAAC;gBACZ1B,OAAO,EAAE,OAAO;gBAChB2B,IAAI,EAAE,OAAO;gBACb4B,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EAMF;AACF,CAAC"}]}