{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749134881813}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\components\\LeftMenu.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;;;IAIL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;;;;;MAKJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE1F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;;IAEN,CAAC,CAAC,CAAC,CAAC;;;IAGJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE1F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;;IAEN,CAAC,CAAC,CAAC,CAAC;;;;IAIJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtG,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;;MAEJ,CAAC,CAAC,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE1F,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC;;IAEN,CAAC,CAAC,CAAC,CAAC;;;IAGJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/G,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/components/LeftMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <!-- 侧边栏 -->\r\n  <nav id=\"sidebar\">\r\n    <div class=\"sidebar-header\">\r\n      <div class=\"d-flex align-items-center p-3\">\r\n        <i class=\"bi bi-shield-check text-white me-2\" style=\"font-size: 1.5rem\"></i>\r\n        <h5 class=\"mb-0\">小区物业管理系统</h5>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '管理员'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n\r\n\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '工作人员'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n          <li><router-link to=\"/usersInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu3\" @click.prevent=\"toggleSubmenu('userSubmenu3')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>公告管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu3') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu3') }\" id=\"userSubmenu3\">\r\n          <li><router-link to=\"/announcementsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage\"> <i class=\"bi bi-circle me-2\"></i>管理公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage2\"> <i class=\"bi bi-circle me-2\"></i>公告列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu4\" @click.prevent=\"toggleSubmenu('userSubmenu4')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>投诉建议管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu4') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu4') }\" id=\"userSubmenu4\">\r\n          <li><router-link to=\"/complaintsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage\"> <i class=\"bi bi-circle me-2\"></i>管理投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage2\"> <i class=\"bi bi-circle me-2\"></i>投诉建议列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu5\" @click.prevent=\"toggleSubmenu('userSubmenu5')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>房屋信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu5') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu5') }\" id=\"userSubmenu5\">\r\n          <li><router-link to=\"/houseAdd\"> <i class=\"bi bi-circle me-2\"></i>添加房屋信息</router-link></li>\r\n          <li><router-link to=\"/houseManage\"> <i class=\"bi bi-circle me-2\"></i>管理房屋信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu7\" @click.prevent=\"toggleSubmenu('userSubmenu7')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>费用信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu7') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu7') }\" id=\"userSubmenu7\">\r\n          <li><router-link to=\"/expenseinfoAdd\"> <i class=\"bi bi-circle me-2\"></i>添加费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage\"> <i class=\"bi bi-circle me-2\"></i>管理费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage2\"> <i class=\"bi bi-circle me-2\"></i>费用信息列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n\r\n    <ul class=\"list-unstyled components\" v-show=\"role == '用户'\">\r\n      <li>\r\n        <a href=\"#userSubmenu1\" @click.prevent=\"toggleSubmenu('userSubmenu1')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>用户管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu1') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu1') }\" id=\"userSubmenu1\">\r\n          <li><router-link to=\"/usersAdd\"> <i class=\"bi bi-circle me-2\"></i>添加用户</router-link></li>\r\n          <li><router-link to=\"/usersManage\"> <i class=\"bi bi-circle me-2\"></i>管理用户</router-link></li>\r\n          <li><router-link to=\"/usersManage2\"> <i class=\"bi bi-circle me-2\"></i>用户列表</router-link></li>\r\n          <li><router-link to=\"/usersInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu3\" @click.prevent=\"toggleSubmenu('userSubmenu3')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>公告管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu3') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu3') }\" id=\"userSubmenu3\">\r\n          <li><router-link to=\"/announcementsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage\"> <i class=\"bi bi-circle me-2\"></i>管理公告</router-link></li>\r\n          <li><router-link to=\"/announcementsManage2\"> <i class=\"bi bi-circle me-2\"></i>公告列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu4\" @click.prevent=\"toggleSubmenu('userSubmenu4')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>投诉建议管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu4') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu4') }\" id=\"userSubmenu4\">\r\n          <li><router-link to=\"/complaintsAdd\"> <i class=\"bi bi-circle me-2\"></i>添加投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage\"> <i class=\"bi bi-circle me-2\"></i>管理投诉建议</router-link></li>\r\n          <li><router-link to=\"/complaintsManage2\"> <i class=\"bi bi-circle me-2\"></i>投诉建议列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu5\" @click.prevent=\"toggleSubmenu('userSubmenu5')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>房屋信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu5') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu5') }\" id=\"userSubmenu5\">\r\n          <li><router-link to=\"/houseAdd\"> <i class=\"bi bi-circle me-2\"></i>添加房屋信息</router-link></li>\r\n          <li><router-link to=\"/houseManage\"> <i class=\"bi bi-circle me-2\"></i>管理房屋信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu6\" @click.prevent=\"toggleSubmenu('userSubmenu6')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>工作人员管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu6') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu6') }\" id=\"userSubmenu6\">\r\n          <li><router-link to=\"/propertystaffAdd\"> <i class=\"bi bi-circle me-2\"></i>添加工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffManage\"> <i class=\"bi bi-circle me-2\"></i>管理工作人员</router-link></li>\r\n          <li><router-link to=\"/propertystaffInfo\"> <i class=\"bi bi-circle me-2\"></i>修改个人信息</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n      <li>\r\n        <a href=\"#userSubmenu7\" @click.prevent=\"toggleSubmenu('userSubmenu7')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>费用信息管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu7') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu7') }\" id=\"userSubmenu7\">\r\n          <li><router-link to=\"/expenseinfoAdd\"> <i class=\"bi bi-circle me-2\"></i>添加费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage\"> <i class=\"bi bi-circle me-2\"></i>管理费用信息</router-link></li>\r\n          <li><router-link to=\"/expenseinfoManage2\"> <i class=\"bi bi-circle me-2\"></i>费用信息列表</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n      <li>\r\n        <a href=\"#userSubmenu26\" @click.prevent=\"toggleSubmenu('userSubmenu26')\"\r\n          class=\"d-flex align-items-center justify-content-between\">\r\n          <div>\r\n            <i class=\"bi bi-people me-2\"></i>\r\n            <span>系统管理</span>\r\n          </div>\r\n          <i class=\"bi bi-chevron-down\" :class=\"{ 'rotate-180': openSubmenus.includes('userSubmenu26') }\"></i>\r\n        </a>\r\n        <ul class=\"collapse list-unstyled\" :class=\"{ show: openSubmenus.includes('userSubmenu26') }\" id=\"userSubmenu26\">\r\n\r\n          <li><router-link to=\"/password\"> <i class=\"bi bi-circle me-2\"></i>修改密码</router-link></li>\r\n\r\n        </ul>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n\r\n    <div class=\"sidebar-footer\">\r\n      <a href=\"javascript:void(0);\" @click=\"exit\" class=\"d-flex align-items-center text-white text-decoration-none p-3\">\r\n        <i class=\"bi bi-box-arrow-left me-2\"></i>\r\n        <span>退出登录</span>\r\n      </a>\r\n    </div>\r\n  </nav>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'LeftMenu',\r\n  data() {\r\n    return {\r\n      userLname: '',\r\n      role: '',\r\n      activeMenuItem: 'dashboard',\r\n      openSubmenus: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem('userLname');\r\n    this.role = sessionStorage.getItem('role');\r\n  },\r\n  methods: {\r\n    setActiveMenuItem(menuItem) {\r\n      this.activeMenuItem = menuItem;\r\n    },\r\n    toggleSubmenu(submenuId) {\r\n      const index = this.openSubmenus.indexOf(submenuId);\r\n      if (index === -1) {\r\n        // Close other submenus before opening new one\r\n        this.openSubmenus = [submenuId];\r\n      } else {\r\n        this.openSubmenus.splice(index, 1);\r\n      }\r\n    },\r\n    exit() {\r\n      this.$confirm('确认退出吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem('userLname');\r\n          sessionStorage.removeItem('role');\r\n          this.$router.push('/');\r\n        })\r\n        .catch(() => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.bi-chevron-down {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.rotate-180 {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.collapse {\r\n  transition: all 0.3s ease-out;\r\n}\r\n\r\n.collapse.show {\r\n  display: block;\r\n}\r\n\r\n.sub-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sub-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sub-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.left-menu {\r\n  width: 250px;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100vh;\r\n  z-index: 999;\r\n  background: #fff;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n#content {\r\n  width: calc(100% - 250px);\r\n  margin-left: 250px;\r\n  transition: all 0.3s;\r\n  min-height: 100vh;\r\n}\r\n\r\n:global(.sidebar-collapsed) #content {\r\n  margin-left: 0;\r\n  width: 100%;\r\n}\r\n\r\n:global(.sidebar-collapsed) .left-menu {\r\n  margin-left: -250px;\r\n}\r\n</style>\r\n\r\n\r\n"]}]}