server:
  port: 8088
  servlet:
    context-path: /CommunityPropertyManagementSystem
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************
    username: root
    password: root
  servlet:
    multipart:
      enabled: true 
      max-file-size: -1 
      max-request-size: -1 
file:
  server:
    dir: upload/
    path: /upload/
mybatis:
  mapper-locations: classpath:/mybatis/mappers/*.xml
  type-aliases-package: com.model
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


