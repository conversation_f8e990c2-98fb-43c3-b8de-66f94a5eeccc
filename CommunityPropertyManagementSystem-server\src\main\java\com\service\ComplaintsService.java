package com.service;
import java.util.List;

import com.model.Complaints;
import com.util.PageBean;

public interface ComplaintsService{
	
	//查询多条记录
	public List<Complaints> queryComplaintsList(Complaints complaints,PageBean page) throws Exception;
 
	//添加
	public int insertComplaints(Complaints complaints) throws Exception ;
	
	//根据ID删除
	public int deleteComplaints(int id) throws Exception ;
	
	//更新
	public int updateComplaints(Complaints complaints) throws Exception ;
	
	//根据ID查询单条数据
	public Complaints queryComplaintsById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Complaints complaints);

}

