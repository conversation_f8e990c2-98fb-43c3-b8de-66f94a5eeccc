<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.PropertystaffMapper">
	<select id="findPropertystaffList"  resultType="Propertystaff">
		select * from propertystaff 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Propertystaff">
	    select  *  
        from propertystaff a  	
		<where>
      		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="plname != null and plname != ''">
		    and a.plname = #{plname}
		</if>
		<if test="pword != null and pword != ''">
		    and a.pword = #{pword}
		</if>
		<if test="pname != null and pname != ''">
		    and a.pname = #{pname}
		</if>
		<if test="gender != null and gender != ''">
		    and a.gender = #{gender}
		</if>
		<if test="age != null and age !=0 ">
		    and a.age = #{age}
		</if>
		<if test="contact != null and contact != ''">
		    and a.contact = #{contact}
		</if>
		<if test="address != null and address != ''">
		    and a.address = #{address}
		</if>
		<if test="regtime != null and regtime != ''">
		    and a.regtime = #{regtime}
		</if>
		<if test="status != null and status != ''">
		    and a.status = #{status}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} pid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from propertystaff a  
		<where>
      		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="plname != null and plname != ''">
		    and a.plname = #{plname}
		</if>
		<if test="pword != null and pword != ''">
		    and a.pword = #{pword}
		</if>
		<if test="pname != null and pname != ''">
		    and a.pname = #{pname}
		</if>
		<if test="gender != null and gender != ''">
		    and a.gender = #{gender}
		</if>
		<if test="age != null and age !=0 ">
		    and a.age = #{age}
		</if>
		<if test="contact != null and contact != ''">
		    and a.contact = #{contact}
		</if>
		<if test="address != null and address != ''">
		    and a.address = #{address}
		</if>
		<if test="regtime != null and regtime != ''">
		    and a.regtime = #{regtime}
		</if>
		<if test="status != null and status != ''">
		    and a.status = #{status}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryPropertystaffById" parameterType="int" resultType="Propertystaff">
    select  *  
     from propertystaff a  	 where a.pid=#{value}
  </select>
 
	<insert id="insertPropertystaff" useGeneratedKeys="true" keyProperty="pid" parameterType="Propertystaff">
    insert into propertystaff
    (plname,pword,pname,gender,age,contact,address,regtime,status)
    values
    (#{plname},#{pword},#{pname},#{gender},#{age},#{contact},#{address},now(),#{status});
  </insert>
	
	<update id="updatePropertystaff" parameterType="Propertystaff" >
    update propertystaff 
    <set>
		<if test="plname != null and plname != ''">
		    plname = #{plname},
		</if>
		<if test="pword != null and pword != ''">
		    pword = #{pword},
		</if>
		<if test="pname != null and pname != ''">
		    pname = #{pname},
		</if>
		<if test="gender != null and gender != ''">
		    gender = #{gender},
		</if>
		<if test="age != null ">
		    age = #{age},
		</if>
		<if test="contact != null and contact != ''">
		    contact = #{contact},
		</if>
		<if test="address != null and address != ''">
		    address = #{address},
		</if>
		<if test="regtime != null and regtime != ''">
		    regtime = #{regtime},
		</if>
		<if test="status != null and status != ''">
		    status = #{status},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="pid != null or pid != ''">
      pid=#{pid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deletePropertystaff" parameterType="int">
    delete from  propertystaff where pid=#{value}
  </delete>

	
	
</mapper>

 
