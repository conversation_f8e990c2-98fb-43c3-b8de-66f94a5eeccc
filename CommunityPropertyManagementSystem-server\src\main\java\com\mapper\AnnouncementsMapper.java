package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Announcements;

public interface AnnouncementsMapper {

	//返回所有记录
	public List<Announcements> findAnnouncementsList();
	
	//查询多条记录
	public List<Announcements> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertAnnouncements(Announcements announcements);

	//根据ID删除
	public int deleteAnnouncements(int id);
	
	//更新
	public int updateAnnouncements(Announcements announcements);
	
	//根据ID得到对应的记录
	public Announcements queryAnnouncementsById(int id);
	
}

