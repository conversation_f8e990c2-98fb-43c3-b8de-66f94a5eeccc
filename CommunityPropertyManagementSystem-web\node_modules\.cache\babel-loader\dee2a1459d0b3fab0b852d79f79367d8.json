{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\utils\\http.js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\utils\\http.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJzsKLy9heGlvcy5kZWZhdWx0cy53aXRoQ3JlZGVudGlhbHMgPSB0cnVlCmV4cG9ydCBjb25zdCBiYXNlID0gJ2h0dHA6Ly8xMjcuMC4wLjE6ODA4OC9Db21tdW5pdHlQcm9wZXJ0eU1hbmFnZW1lbnRTeXN0ZW0vYXBpJzsKY29uc3QgcmVxdWVzdCA9IGF4aW9zLmNyZWF0ZSh7CiAgdGltZW91dDogNTAwMAp9KTsKcmVxdWVzdC5pbnRlcmNlcHRvcnMucmVxdWVzdC51c2UoY29uZmlnID0+IHsKICBjb25maWcuaGVhZGVyc1snQ29udGVudC1UeXBlJ10gPSAnYXBwbGljYXRpb24vanNvbjtjaGFyc2V0PXV0Zi04JzsKICAvLyBjb25maWcuaGVhZGVyc1sndG9rZW4nXSA9IHVzZXIudG9rZW47ICAvLyDorr7nva7or7fmsYLlpLQKICByZXR1cm4gY29uZmlnOwp9LCBlcnJvciA9PiB7CiAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTsKfSk7CgovLyDlj6/ku6XlnKjmjqXlj6Plk43lupTlkI7nu5/kuIDlpITnkIbnu5PmnpwKcmVxdWVzdC5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKHJlc3BvbnNlID0+IHsKICBsZXQgcmVzID0gcmVzcG9uc2UuZGF0YTsKICAvLyDlpoLmnpzmmK/ov5Tlm57nmoTmlofku7YKICBpZiAocmVzcG9uc2UuY29uZmlnLnJlc3BvbnNlVHlwZSA9PT0gJ2Jsb2InKSB7CiAgICByZXR1cm4gcmVzOwogIH0KICAvLyDlhbzlrrnmnI3liqHnq6/ov5Tlm57nmoTlrZfnrKbkuLLmlbDmja4KICBpZiAodHlwZW9mIHJlcyA9PT0gJ3N0cmluZycpIHsKICAgIHJlcyA9IHJlcyA/IEpTT04ucGFyc2UocmVzKSA6IHJlczsKICB9CiAgcmV0dXJuIHJlczsKfSwgZXJyb3IgPT4gewogIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7Cn0pOwpleHBvcnQgZGVmYXVsdCByZXF1ZXN0Ow=="}, {"version": 3, "names": ["axios", "base", "request", "create", "timeout", "interceptors", "use", "config", "headers", "error", "Promise", "reject", "response", "res", "data", "responseType", "JSON", "parse"], "sources": ["I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/utils/http.js"], "sourcesContent": ["import axios from 'axios';\r\n//axios.defaults.withCredentials = true\r\nexport const base = 'http://127.0.0.1:8088/CommunityPropertyManagementSystem/api';\r\n\r\nconst request = axios.create({\r\n    timeout: 5000\r\n})\r\n\r\nrequest.interceptors.request.use(config => {\r\n    config.headers['Content-Type'] = 'application/json;charset=utf-8';\r\n    // config.headers['token'] = user.token;  // 设置请求头\r\n    return config\r\n}, error => {\r\n    return Promise.reject(error)\r\n});\r\n\r\n// 可以在接口响应后统一处理结果\r\nrequest.interceptors.response.use(\r\n    response => {\r\n        let res = response.data;\r\n        // 如果是返回的文件\r\n        if (response.config.responseType === 'blob') {\r\n            return res\r\n        }\r\n        // 兼容服务端返回的字符串数据\r\n        if (typeof res === 'string') {\r\n            res = res ? JSON.parse(res) : res\r\n        }\r\n        return res;\r\n    },\r\n    error => {\r\n        return Promise.reject(error)\r\n    }\r\n)\r\n\r\nexport default request\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,OAAO,MAAMC,IAAI,GAAG,6DAA6D;AAEjF,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAAC;EACzBC,OAAO,EAAE;AACb,CAAC,CAAC;AAEFF,OAAO,CAACG,YAAY,CAACH,OAAO,CAACI,GAAG,CAACC,MAAM,IAAI;EACvCA,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;EACjE;EACA,OAAOD,MAAM;AACjB,CAAC,EAAEE,KAAK,IAAI;EACR,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CAAC;;AAEF;AACAP,OAAO,CAACG,YAAY,CAACO,QAAQ,CAACN,GAAG,CAC7BM,QAAQ,IAAI;EACR,IAAIC,GAAG,GAAGD,QAAQ,CAACE,IAAI;EACvB;EACA,IAAIF,QAAQ,CAACL,MAAM,CAACQ,YAAY,KAAK,MAAM,EAAE;IACzC,OAAOF,GAAG;EACd;EACA;EACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzBA,GAAG,GAAGA,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,GAAGA,GAAG;EACrC;EACA,OAAOA,GAAG;AACd,CAAC,EACDJ,KAAK,IAAI;EACL,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;AAED,eAAeP,OAAO"}]}