{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue?vue&type=template&id=a44c444e", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdG9EaXNwbGF5U3RyaW5nIGFzIF90b0Rpc3BsYXlTdHJpbmcsIGNyZWF0ZUVsZW1lbnRWTm9kZSBhcyBfY3JlYXRlRWxlbWVudFZOb2RlLCBjcmVhdGVUZXh0Vk5vZGUgYXMgX2NyZWF0ZVRleHRWTm9kZSwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICBzdHlsZTogewogICAgIndpZHRoIjogIjEwMCUiLAogICAgImxpbmUtaGVpZ2h0IjogIjMwcHgiLAogICAgInRleHQtYWxpZ24iOiAiY2VudGVyIiwKICAgICJwYWRkaW5nIjogIjEwMHB4IgogIH0sCiAgaWQ6ICJob21lIgp9Owpjb25zdCBfaG9pc3RlZF8yID0gLyojX19QVVJFX18qL19jcmVhdGVUZXh0Vk5vZGUoIiDotKblj7fvvJoiKTsKY29uc3QgX2hvaXN0ZWRfMyA9IHsKICBzdHlsZTogewogICAgImNvbG9yIjogInJlZCIKICB9Cn07CmNvbnN0IF9ob2lzdGVkXzQgPSAvKiNfX1BVUkVfXyovX2NyZWF0ZVRleHRWTm9kZSgi77yMIOi6q+S7ve+8miIpOwpjb25zdCBfaG9pc3RlZF81ID0gewogIHN0eWxlOiB7CiAgICAiY29sb3IiOiAicmVkIgogIH0KfTsKY29uc3QgX2hvaXN0ZWRfNiA9IC8qI19fUFVSRV9fKi9fY3JlYXRlRWxlbWVudFZOb2RlKCJiciIsIG51bGwsIG51bGwsIC0xIC8qIEhPSVNURUQgKi8pOwpjb25zdCBfaG9pc3RlZF83ID0gLyojX19QVVJFX18qL19jcmVhdGVUZXh0Vk5vZGUoIiDmgqjlpb3vvIzmrKLov47kvb/nlKjlsI/ljLrniankuJrnrqHnkIbns7vnu5/vvIEiKTsKY29uc3QgX2hvaXN0ZWRfOCA9IC8qI19fUFVSRV9fKi9fY3JlYXRlRWxlbWVudFZOb2RlKCJiciIsIG51bGwsIG51bGwsIC0xIC8qIEhPSVNURUQgKi8pOwoKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xLCBbX2hvaXN0ZWRfMiwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYiIsIF9ob2lzdGVkXzMsIF90b0Rpc3BsYXlTdHJpbmcoJGRhdGEudXNlckxuYW1lKSwgMSAvKiBURVhUICovKSwgX2hvaXN0ZWRfNCwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiYiIsIF9ob2lzdGVkXzUsIF90b0Rpc3BsYXlTdHJpbmcoJGRhdGEucm9sZSksIDEgLyogVEVYVCAqLyksIF9ob2lzdGVkXzYsIF9ob2lzdGVkXzcsIF9ob2lzdGVkXzhdKTsKfQ=="}, {"version": 3, "names": ["style", "id", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_hoisted_3", "_toDisplayString", "$data", "userLname", "_hoisted_5", "role", "_hoisted_6", "_hoisted_8"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue"], "sourcesContent": ["<template>\r\n\r\n                 \r\n  \r\n\r\n  <div style=\"width: 100%;line-height: 30px;text-align: center; padding: 100px;\" id=\"home\">\r\n\r\n\r\n    账号：<b style=\"color: red;\">{{ userLname }}</b>，\r\n    身份：<b style=\"color: red;\">{{ role }}</b><br>\r\n\r\n\r\n\r\n    您好，欢迎使用小区物业管理系统！<br>\r\n\r\n\r\n  </div>\r\n\r\n\r\n\r\n \r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        userLname: \"\",\r\n        role: \"\",\r\n      };\r\n    },\r\n    mounted() {\r\n      this.userLname = sessionStorage.getItem(\"userLname\");\r\n      this.role = sessionStorage.getItem(\"role\");  \r\n\r\n    },\r\n  };\r\n\r\n</script>\r\n\r\n<style scoped></style>\r\n\r\n"], "mappings": ";;EAKOA,KAAyE,EAAzE;IAAA;IAAA;IAAA;IAAA;EAAA,CAAyE;EAACC,EAAE,EAAC;;iDAAO,MAGpF;;EAAGD,KAAmB,EAAnB;IAAA;EAAA;AAAmB;iDAAoB,OAC1C;;EAAGA,KAAmB,EAAnB;IAAA;EAAA;AAAmB;gCAAeE,mBAAA,CAAI;iDAAA,mBAI5B;gCAAAA,mBAAA,CAAI;;;uBARtBC,mBAAA,CAWM,OAXNC,UAWM,G,YARDF,mBAAA,CAA0C,KAA1CG,UAA0C,EAAAC,gBAAA,CAAhBC,KAAA,CAAAC,SAAS,kB,YACnCN,mBAAA,CAAqC,KAArCO,UAAqC,EAAAH,gBAAA,CAAXC,KAAA,CAAAG,IAAI,kBAAOC,UAAI,E,YAI5BC,UAAI,C"}]}