{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseAdd.vue?vue&type=template&id=516b616a", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseAdd.vue", "mtime": 1749135666880}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMTAwJTtsaW5lLWhlaWdodDogMzBweDt0ZXh0LWFsaWduOiBsZWZ0OyI+CiAgICAgICA8ZWwtZm9ybSA6bW9kZWw9ImZvcm1EYXRhIiBsYWJlbC13aWR0aD0iMjAlIiByZWY9ImZvcm1EYXRhUmVmIiA6cnVsZXM9ImFkZHJ1bGVzIiAgYWxpZ249ImxlZnQiPgo8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpl6jniYzlj7ciIHByb3A9ImhubyI+CjxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtRGF0YS5obm8iIHBsYWNlaG9sZGVyPSLpl6jniYzlj7ciICBzdHlsZT0id2lkdGg6NTAlOyIgPjwvZWwtaW5wdXQ+CjwvZWwtZm9ybS1pdGVtPgo8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmiYDlsZ7mpbzmoIsiIHByb3A9ImJ1aWxkaW5mbyI+CjxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtRGF0YS5idWlsZGluZm8iIHBsYWNlaG9sZGVyPSLmiYDlsZ7mpbzmoIsiICBzdHlsZT0id2lkdGg6NTAlOyIgPjwvZWwtaW5wdXQ+CjwvZWwtZm9ybS1pdGVtPgo8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmiLflnosiIHByb3A9Imh0eXBlIj4KPGVsLWlucHV0IHYtbW9kZWw9ImZvcm1EYXRhLmh0eXBlIiBwbGFjZWhvbGRlcj0i5oi35Z6LIiAgc3R5bGU9IndpZHRoOjUwJTsiID48L2VsLWlucHV0Pgo8L2VsLWZvcm0taXRlbT4KPGVsLWZvcm0taXRlbSBsYWJlbD0i6Z2i56evL20yIiBwcm9wPSJhcmVhc2luZm8iPgo8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybURhdGEuYXJlYXNpbmZvIiBwbGFjZWhvbGRlcj0i6Z2i56evL20yIiAgc3R5bGU9IndpZHRoOjUwJTsiID48L2VsLWlucHV0Pgo8L2VsLWZvcm0taXRlbT4KPGVsLWZvcm0taXRlbSBsYWJlbD0i5pyd5ZCRIiBwcm9wPSJob3JkIj4KPGVsLWlucHV0IHYtbW9kZWw9ImZvcm1EYXRhLmhvcmQiIHBsYWNlaG9sZGVyPSLmnJ3lkJEiICBzdHlsZT0id2lkdGg6NTAlOyIgPjwvZWwtaW5wdXQ+CjwvZWwtZm9ybS1pdGVtPgoKPGVsLWZvcm0taXRlbT4KPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBzaXplPSJzbWFsbCIgQGNsaWNrPSJzYXZlIiA6bG9hZGluZz0iYnRuTG9hZGluZyIgaWNvbj0iZWwtaWNvbi11cGxvYWQiPuaPkCDkuqQ8L2VsLWJ1dHRvbj4KPGVsLWJ1dHRvbiB0eXBlPSJpbmZvIiBzaXplPSJzbWFsbCIgQGNsaWNrPSJnb0JhY2siIGljb249ImVsLWljb24tYmFjayI+6L+UIOWbnjwvZWwtYnV0dG9uPgo8L2VsLWZvcm0taXRlbT4KPC9lbC1mb3JtPgoKCiAgICA8L2Rpdj4K"}, {"version": 3, "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\house\\HouseAdd.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;IAGL,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/views/admin/house/HouseAdd.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\n<el-form-item label=\"门牌号\" prop=\"hno\">\n<el-input v-model=\"formData.hno\" placeholder=\"门牌号\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"所属楼栋\" prop=\"buildinfo\">\n<el-input v-model=\"formData.buildinfo\" placeholder=\"所属楼栋\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"户型\" prop=\"htype\">\n<el-input v-model=\"formData.htype\" placeholder=\"户型\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"面积/m2\" prop=\"areasinfo\">\n<el-input v-model=\"formData.areasinfo\" placeholder=\"面积/m2\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"朝向\" prop=\"hord\">\n<el-input v-model=\"formData.hord\" placeholder=\"朝向\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\n</el-form-item>\n</el-form>\n\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'HouseAdd',\n  components: {\n    \n  },  \n    data() {\n      return {   \n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\n          hno: [{ required: true, message: '请输入门牌号', trigger: 'blur' },\n],          buildinfo: [{ required: true, message: '请输入所属楼栋', trigger: 'blur' },\n],          htype: [{ required: true, message: '请输入户型', trigger: 'blur' },\n],          areasinfo: [{ required: true, message: '请输入面积/m2', trigger: 'blur' },\n],          hord: [{ required: true, message: '请输入朝向', trigger: 'blur' },\n],          uid: [{ required: true, message: '请输入用户id', trigger: 'blur' },\n],        },\n\n      };\n    },\n    mounted() {\n    \n    },\n\n \n    methods: {    \n   // 添加\n    save() {       \n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n           if (valid) {\n             let url = base + \"/house/add\";\n             this.btnLoading = true;\n             us\n             request.post(url, this.formData).then((res) => { //发送请求         \n               if (res.code == 200) {\n                 this.$message({\n                   message: \"操作成功\",\n                   type: \"success\",\n                   offset: 320,\n                 });              \n                this.$router.push({\n                path: \"/HouseManage\",\n                });\n               } else {\n                 this.$message({\n                   message: res.msg,\n                   type: \"error\",\n                   offset: 320,\n                 });\n               }\n               this.btnLoading=false;\n             });\n           }        \n           \n         });\n    },\n    \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/HouseManage\",\n          });\n        },       \n              \n          \n           \n           \n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"]}]}