{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\complaints\\ComplaintsAdd.vue?vue&type=template&id=23191bec", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\complaints\\ComplaintsAdd.vue", "mtime": 1749135778786}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_select", "ctype", "$event", "placeholder", "size", "_component_el_option", "value", "_component_el_input", "ctitle", "_component_WangEditor", "ccontent", "config", "_ctx", "editorConfig", "isClear", "onChange", "$options", "<PERSON><PERSON><PERSON><PERSON>", "_component_el_button", "type", "onClick", "save", "loading", "btnLoading", "icon", "goBack"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\complaints\\ComplaintsAdd.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\n<el-form-item label=\"类别\" prop=\"ctype\">\n<el-select v-model=\"formData.ctype\" placeholder=\"请选择\"  size=\"small\">\n<el-option label=\"投诉\" value=\"投诉\"></el-option>\n<el-option label=\"建议\" value=\"建议\"></el-option>\n</el-select>\n</el-form-item>\n<el-form-item label=\"标题\" prop=\"ctitle\">\n<el-input v-model=\"formData.ctitle\" placeholder=\"标题\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"具体描述\" prop=\"ccontent\">\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.ccontent\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\n</el-form-item>\n\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\n</el-form-item>\n</el-form>\n\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport WangEditor from \"../../../components/WangEditor\";\nexport default {\n  name: 'ComplaintsAdd',\n  components: {\n    WangEditor,\n  },  \n    data() {\n      return {   \n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\n          ctype: [{ required: true, message: '请选择类别', trigger: 'onchange' }],\n          ctitle: [{ required: true, message: '请输入标题', trigger: 'blur' },\n],          uid: [{ required: true, message: '请输入用户id', trigger: 'blur' },\n],          cstatus: [{ required: true, message: '请输入处理状态', trigger: 'blur' },\n],          cresult: [{ required: true, message: '请输入处理结果', trigger: 'blur' },\n],        },\n\n      };\n    },\n    mounted() {\n    \n    },\n\n \n    methods: {    \n   // 添加\n    save() {       \n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n           if (valid) {\n             let url = base + \"/complaints/add\";\n             this.btnLoading = true;\n             var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\n             this.formData.uid = user.uid;\n             \n             request.post(url, this.formData).then((res) => { //发送请求         \n               if (res.code == 200) {\n                 this.$message({\n                   message: \"操作成功\",\n                   type: \"success\",\n                   offset: 320,\n                 });              \n                this.$router.push({\n                path: \"/ComplaintsManage\",\n                });\n               } else {\n                 this.$message({\n                   message: res.msg,\n                   type: \"error\",\n                   offset: 320,\n                 });\n               }\n               this.btnLoading=false;\n             });\n           }        \n           \n         });\n    },\n    \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/ComplaintsManage\",\n          });\n        },       \n              \n          \n           \n            // 富文本编辑器\n    editorChange(val) {\n      this.formData.ccontent = val;\n    },\n   \n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;iDAgBiC,KAAG;iDAC5B,KAAG;;;;;;;;;uBAjBvEC,mBAAA,CAsBM,OAtBNC,UAsBM,GArBHC,YAAA,CAkBGC,kBAAA;IAlBOC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAC/F,MAKe,CALfR,YAAA,CAKeS,uBAAA;MALDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAGY,CAHZX,YAAA,CAGYY,oBAAA;oBAHQT,KAAA,CAAAC,QAAQ,CAACS,KAAK;mEAAdV,KAAA,CAAAC,QAAQ,CAACS,KAAK,GAAAC,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAEC,IAAI,EAAC;;0BAC5D,MAA6C,CAA7ChB,YAAA,CAA6CiB,oBAAA;UAAlCP,KAAK,EAAC,IAAI;UAACQ,KAAK,EAAC;YAC5BlB,YAAA,CAA6CiB,oBAAA;UAAlCP,KAAK,EAAC,IAAI;UAACQ,KAAK,EAAC;;;;;QAG5BlB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAqF,CAArFX,YAAA,CAAqFmB,mBAAA;oBAAlEhB,KAAA,CAAAC,QAAQ,CAACgB,MAAM;mEAAfjB,KAAA,CAAAC,QAAQ,CAACgB,MAAM,GAAAN,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEtDG,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;wBAChC,MAA6I,CAA7IX,YAAA,CAA6IqB,qBAAA;QAAhIhB,GAAG,EAAC,eAAe;oBAAUF,KAAA,CAAAC,QAAQ,CAACkB,QAAQ;mEAAjBnB,KAAA,CAAAC,QAAQ,CAACkB,QAAQ,GAAAR,MAAA;QAAGS,MAAM,EAAEC,IAAA,CAAAC,YAAY;QAAKC,OAAO,EAAEF,IAAA,CAAAE,OAAO;QAAGC,QAAM,EAAEC,QAAA,CAAAC;;;QAGlH7B,YAAA,CAGeS,uBAAA;wBAFf,MAAgH,CAAhHT,YAAA,CAAgH8B,oBAAA;QAArGC,IAAI,EAAC,SAAS;QAACf,IAAI,EAAC,OAAO;QAAEgB,OAAK,EAAEJ,QAAA,CAAAK,IAAI;QAAGC,OAAO,EAAE/B,KAAA,CAAAgC,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAG,C;;iDACpGpC,YAAA,CAAuF8B,oBAAA;QAA5EC,IAAI,EAAC,MAAM;QAACf,IAAI,EAAC,OAAO;QAAEgB,OAAK,EAAEJ,QAAA,CAAAS,MAAM;QAAED,IAAI,EAAC;;0BAAe,MAAG,C"}]}