{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue?vue&type=template&id=a44c444e&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749136123922}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["id", "class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_6", "_toDisplayString", "$data", "userLname", "_hoisted_8", "role", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "statistics", "userCount", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "staffCount", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "announcementCount", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "complaintCount", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "expenseCount", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "houseCount", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\admin\\Home.vue"], "sourcesContent": ["<template>\r\n  <div id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div class=\"welcome-section\">\r\n      <h2 class=\"welcome-title\">欢迎使用小区物业管理系统</h2>\r\n      <p class=\"welcome-info\">\r\n        账号：<b style=\"color: #409EFF;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #409EFF;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片区域 -->\r\n    <div class=\"statistics-container\">\r\n      <div class=\"row\">\r\n        <!-- 管理员统计卡片 -->\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '管理员'\" class=\"col-md-6 col-lg-6\">\r\n          <div class=\"stat-card staff-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-person-badge-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.staffCount || 0 }}</h3>\r\n              <p>工作人员数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 工作人员统计卡片 -->\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card user-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-people-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.userCount || 0 }}</h3>\r\n              <p>用户数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card announcement-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-megaphone-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.announcementCount || 0 }}</h3>\r\n              <p>公告信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '工作人员'\" class=\"col-md-6 col-lg-3\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 用户统计卡片 -->\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card house-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-house-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.houseCount || 0 }}</h3>\r\n              <p>房屋信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card expense-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-currency-dollar\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.expenseCount || 0 }}</h3>\r\n              <p>费用信息数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"role === '用户'\" class=\"col-md-4\">\r\n          <div class=\"stat-card complaint-card\">\r\n            <div class=\"stat-icon\">\r\n              <i class=\"bi bi-chat-square-text-fill\"></i>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3>{{ statistics.complaintCount || 0 }}</h3>\r\n              <p>投诉建议数量</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      statistics: {},\r\n      loading: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        let url = \"\";\r\n        let params = {};\r\n\r\n        if (this.role === \"管理员\") {\r\n          url = base + \"/statistics/admin\";\r\n        } else if (this.role === \"工作人员\") {\r\n          url = base + \"/statistics/staff\";\r\n        } else if (this.role === \"用户\") {\r\n          url = base + \"/statistics/usertotal\";\r\n          // 获取当前用户ID\r\n          const user = JSON.parse(sessionStorage.getItem(\"user\") || \"{}\");\r\n          params.userId = user.uid;\r\n        }\r\n\r\n        if (url) {\r\n          const response = await request.post(url, params);\r\n          if (response.code === 200) {\r\n            this.statistics = response.resdata;\r\n          } else {\r\n            this.$message({\r\n              message: response.msg || \"获取统计数据失败\",\r\n              type: \"error\",\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取统计数据失败:\", error);\r\n        this.$message({\r\n          message: \"获取统计数据失败\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n#home {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.welcome-section {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 30px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 15px;\r\n  color: white;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 2.5rem;\r\n  font-weight: 600;\r\n  margin-bottom: 15px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.welcome-info {\r\n  font-size: 1.2rem;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.statistics-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: -15px;\r\n}\r\n\r\n.col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n  padding: 15px;\r\n  flex: 1;\r\n  min-width: 280px;\r\n}\r\n\r\n.col-md-4 {\r\n  flex: 0 0 33.333333%;\r\n}\r\n\r\n.col-md-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.col-lg-3 {\r\n  flex: 0 0 25%;\r\n}\r\n\r\n.col-lg-6 {\r\n  flex: 0 0 50%;\r\n}\r\n\r\n.stat-card {\r\n  background: white;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 120px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stat-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));\r\n}\r\n\r\n.stat-icon {\r\n  font-size: 3rem;\r\n  margin-right: 20px;\r\n  color: var(--card-color);\r\n  opacity: 0.8;\r\n}\r\n\r\n.stat-content h3 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 5px 0;\r\n  color: #2c3e50;\r\n}\r\n\r\n.stat-content p {\r\n  font-size: 1rem;\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 不同卡片的颜色主题 */\r\n.user-card {\r\n  --card-color: #3498db;\r\n  --card-color-light: #5dade2;\r\n}\r\n\r\n.staff-card {\r\n  --card-color: #2ecc71;\r\n  --card-color-light: #58d68d;\r\n}\r\n\r\n.announcement-card {\r\n  --card-color: #f39c12;\r\n  --card-color-light: #f8c471;\r\n}\r\n\r\n.complaint-card {\r\n  --card-color: #e74c3c;\r\n  --card-color-light: #ec7063;\r\n}\r\n\r\n.expense-card {\r\n  --card-color: #9b59b6;\r\n  --card-color-light: #bb8fce;\r\n}\r\n\r\n.house-card {\r\n  --card-color: #1abc9c;\r\n  --card-color-light: #5dccb4;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .col-md-4, .col-md-6, .col-lg-3, .col-lg-6 {\r\n    flex: 0 0 100%;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .stat-card {\r\n    height: auto;\r\n    min-height: 100px;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    font-size: 2.5rem;\r\n    margin-right: 15px;\r\n  }\r\n\r\n  .stat-content h3 {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  #home {\r\n    padding: 10px;\r\n  }\r\n\r\n  .welcome-section {\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .welcome-info {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .stat-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    height: auto;\r\n    padding: 20px;\r\n  }\r\n\r\n  .stat-icon {\r\n    margin-right: 0;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;EACOA,EAAE,EAAC;AAAM;;EAEPC,KAAK,EAAC;AAAiB;gEAC1BC,mBAAA,CAA2C;EAAvCD,KAAK,EAAC;AAAe,GAAC,cAAY;;EACnCA,KAAK,EAAC;AAAc;iDAAC,MACnB;;EAAGE,KAAuB,EAAvB;IAAA;EAAA;AAAuB;iDAAoB,OAC9C;;EAAGA,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;EAK5BF,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAK;;;EAEaA,KAAK,EAAC;;;EAC1BA,KAAK,EAAC;AAAqB;iEAC9BC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,G;;EAEzBA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAW,WAAR,MAAI;;;EAKcD,KAAK,EAAC;;;EAC1BA,KAAK,EAAC;AAAsB;iEAC/BC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAAuC;EAApCD,KAAK,EAAC;AAAyB,G;;EAE/BA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAa,WAAV,QAAM;;;EAMaD,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAqB;iEAC9BC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAAiC;EAA9BD,KAAK,EAAC;AAAmB,G;;EAEzBA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAW,WAAR,MAAI;;;EAKeD,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAA6B;iEACtCC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAAoC;EAAjCD,KAAK,EAAC;AAAsB,G;;EAE5BA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAa,WAAV,QAAM;;;EAKaD,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAA0B;iEACnCC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAA2C;EAAxCD,KAAK,EAAC;AAA6B,G;;EAEnCA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAa,WAAV,QAAM;;;EAKaD,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAwB;iEACjCC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAAqC;EAAlCD,KAAK,EAAC;AAAuB,G;;EAE7BA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAa,WAAV,QAAM;;;EAMWD,KAAK,EAAC;;;EACzBA,KAAK,EAAC;AAAsB;iEAC/BC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAAgC;EAA7BD,KAAK,EAAC;AAAkB,G;;EAExBA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAa,WAAV,QAAM;;;EAKWD,KAAK,EAAC;;;EACzBA,KAAK,EAAC;AAAwB;iEACjCC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAAqC;EAAlCD,KAAK,EAAC;AAAuB,G;;EAE7BA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAa,WAAV,QAAM;;;EAKWD,KAAK,EAAC;;;EACzBA,KAAK,EAAC;AAA0B;iEACnCC,mBAAA,CAEM;EAFDD,KAAK,EAAC;AAAW,I,aACpBC,mBAAA,CAA2C;EAAxCD,KAAK,EAAC;AAA6B,G;;EAEnCA,KAAK,EAAC;AAAc;iEAEvBC,mBAAA,CAAa,WAAV,QAAM;;;uBAvHrBE,mBAAA,CA6HM,OA7HNC,UA6HM,GA5HJC,mBAAA,UAAa,EACbJ,mBAAA,CAMM,OANNK,UAMM,GALJC,UAA2C,EAC3CN,mBAAA,CAGI,KAHJO,UAGI,G,YAFCP,mBAAA,CAA8C,KAA9CQ,UAA8C,EAAAC,gBAAA,CAAhBC,KAAA,CAAAC,SAAS,kB,YACvCX,mBAAA,CAAyC,KAAzCY,UAAyC,EAAAH,gBAAA,CAAXC,KAAA,CAAAG,IAAI,iB,KAIzCT,mBAAA,YAAe,EACfJ,mBAAA,CAiHM,OAjHNc,UAiHM,GAhHJd,mBAAA,CA+GM,OA/GNe,WA+GM,GA9GJX,mBAAA,aAAgB,EACLM,KAAA,CAAAG,IAAI,c,cAAfX,mBAAA,CAUM,OAVNc,WAUM,GATJhB,mBAAA,CAQM,OARNiB,WAQM,GAPJC,WAEM,EACNlB,mBAAA,CAGM,OAHNmB,WAGM,GAFJnB,mBAAA,CAAwC,YAAAS,gBAAA,CAAjCC,KAAA,CAAAU,UAAU,CAACC,SAAS,uBAC3BC,WAAW,C,4CAKNZ,KAAA,CAAAG,IAAI,c,cAAfX,mBAAA,CAUM,OAVNqB,WAUM,GATJvB,mBAAA,CAQM,OARNwB,WAQM,GAPJC,WAEM,EACNzB,mBAAA,CAGM,OAHN0B,WAGM,GAFJ1B,mBAAA,CAAyC,YAAAS,gBAAA,CAAlCC,KAAA,CAAAU,UAAU,CAACO,UAAU,uBAC5BC,WAAa,C,4CAKnBxB,mBAAA,cAAiB,EACNM,KAAA,CAAAG,IAAI,e,cAAfX,mBAAA,CAUM,OAVN2B,WAUM,GATJ7B,mBAAA,CAQM,OARN8B,WAQM,GAPJC,WAEM,EACN/B,mBAAA,CAGM,OAHNgC,WAGM,GAFJhC,mBAAA,CAAwC,YAAAS,gBAAA,CAAjCC,KAAA,CAAAU,UAAU,CAACC,SAAS,uBAC3BY,WAAW,C,4CAKNvB,KAAA,CAAAG,IAAI,e,cAAfX,mBAAA,CAUM,OAVNgC,WAUM,GATJlC,mBAAA,CAQM,OARNmC,WAQM,GAPJC,WAEM,EACNpC,mBAAA,CAGM,OAHNqC,WAGM,GAFJrC,mBAAA,CAAgD,YAAAS,gBAAA,CAAzCC,KAAA,CAAAU,UAAU,CAACkB,iBAAiB,uBACnCC,WAAa,C,4CAKR7B,KAAA,CAAAG,IAAI,e,cAAfX,mBAAA,CAUM,OAVNsC,WAUM,GATJxC,mBAAA,CAQM,OARNyC,WAQM,GAPJC,WAEM,EACN1C,mBAAA,CAGM,OAHN2C,WAGM,GAFJ3C,mBAAA,CAA6C,YAAAS,gBAAA,CAAtCC,KAAA,CAAAU,UAAU,CAACwB,cAAc,uBAChCC,WAAa,C,4CAKRnC,KAAA,CAAAG,IAAI,e,cAAfX,mBAAA,CAUM,OAVN4C,WAUM,GATJ9C,mBAAA,CAQM,OARN+C,WAQM,GAPJC,WAEM,EACNhD,mBAAA,CAGM,OAHNiD,WAGM,GAFJjD,mBAAA,CAA2C,YAAAS,gBAAA,CAApCC,KAAA,CAAAU,UAAU,CAAC8B,YAAY,uBAC9BC,WAAa,C,4CAKnB/C,mBAAA,YAAe,EACJM,KAAA,CAAAG,IAAI,a,cAAfX,mBAAA,CAUM,OAVNkD,WAUM,GATJpD,mBAAA,CAQM,OARNqD,WAQM,GAPJC,WAEM,EACNtD,mBAAA,CAGM,OAHNuD,WAGM,GAFJvD,mBAAA,CAAyC,YAAAS,gBAAA,CAAlCC,KAAA,CAAAU,UAAU,CAACoC,UAAU,uBAC5BC,WAAa,C,4CAKR/C,KAAA,CAAAG,IAAI,a,cAAfX,mBAAA,CAUM,OAVNwD,WAUM,GATJ1D,mBAAA,CAQM,OARN2D,WAQM,GAPJC,WAEM,EACN5D,mBAAA,CAGM,OAHN6D,WAGM,GAFJ7D,mBAAA,CAA2C,YAAAS,gBAAA,CAApCC,KAAA,CAAAU,UAAU,CAAC8B,YAAY,uBAC9BY,WAAa,C,4CAKRpD,KAAA,CAAAG,IAAI,a,cAAfX,mBAAA,CAUM,OAVN6D,WAUM,GATJ/D,mBAAA,CAQM,OARNgE,WAQM,GAPJC,WAEM,EACNjE,mBAAA,CAGM,OAHNkE,WAGM,GAFJlE,mBAAA,CAA6C,YAAAS,gBAAA,CAAtCC,KAAA,CAAAU,UAAU,CAACwB,cAAc,uBAChCuB,WAAa,C"}]}