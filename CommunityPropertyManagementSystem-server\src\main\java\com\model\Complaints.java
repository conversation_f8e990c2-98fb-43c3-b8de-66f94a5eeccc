package com.model;
import java.util.List;

/**
* (complaints)投诉建议实体类
*/
public class Complaints extends ComData{
	
	private static final long serialVersionUID = 647337615285553L;
	private Integer cid;    //id
	private String ctype;    //类别
	private String ctitle;    //标题
	private String ccontent;    //具体描述
	private Integer uid;    //用户id
	private String ctime;    //提交时间
	private String cstatus;    //处理状态
	private String cresult;    //处理结果

	public Integer getCid() {
		return cid;
	}

	public void setCid(Integer cid) {
		this.cid = cid;
	}

	public String getCtype() {
		return ctype;
	}

	public void setCtype(String ctype) {
		this.ctype = ctype;
	}

	public String getCtitle() {
		return ctitle;
	}

	public void setCtitle(String ctitle) {
		this.ctitle = ctitle;
	}

	public String getCcontent() {
		return ccontent;
	}

	public void setCcontent(String ccontent) {
		this.ccontent = ccontent;
	}

	public Integer getUid() {
		return uid;
	}

	public void setUid(Integer uid) {
		this.uid = uid;
	}

	public String getCtime() {
		return ctime;
	}

	public void setCtime(String ctime) {
		this.ctime = ctime;
	}

	public String getCstatus() {
		return cstatus;
	}

	public void setCstatus(String cstatus) {
		this.cstatus = cstatus;
	}

	public String getCresult() {
		return cresult;
	}

	public void setCresult(String cresult) {
		this.cresult = cresult;
	}

}

