{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue?vue&type=template&id=c1f1971a&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "id", "_createBlock", "_component_el_config_provider", "locale", "$data", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_LeftMenu", "_hoisted_2", "_component_Header", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "$route", "meta", "title", "_hoisted_9", "_component_router_view"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n  <el-config-provider :locale=\"locale\">\r\n    <div class=\"wrapper\">\r\n      <LeftMenu />\r\n\r\n      <div id=\"content\">\r\n        <Header class=\"header-fixed\" />\r\n\r\n        <div class=\"main-content\">\r\n          <div class=\"container-fluid p-4\">\r\n            <div class=\"page-container\">\r\n              <div class=\"content-container\">\r\n                <div class=\"page-header d-flex justify-content-between align-items-center\">\r\n                  <h5 class=\"page-title\">{{ this.$route.meta.title }}</h5>\r\n                </div>\r\n\r\n                <div class=\"table-responsive\"><router-view /></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-config-provider>\r\n</template>\r\n\r\n<script>\r\nimport Header from '../components/Header';\r\nimport LeftMenu from '../components/LeftMenu';\r\nimport { ElConfigProvider } from 'element-plus';\r\nimport zhCn from 'element-plus/lib/locale/lang/zh-cn';\r\nimport $ from 'jquery';\r\nexport default {\r\n  name: 'MainLayout',\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n  },\r\n\r\n  methods: {},\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n@import url(../assets/css/ht_bootstrap.min.css);\r\n@import url(../assets/css/ht_bootstrap-icons.css);\r\n@import url(../assets/css/ht_style.css);\r\n\r\n.wrapper {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  position: relative;\r\n}\r\n\r\n#content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  position: relative;\r\n}\r\n\r\n.header-fixed {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  left: var(--sidebar-width);\r\n  z-index: 100;\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  padding-top: var(--header-height);\r\n  overflow-y: auto;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.table-responsive {\r\n  position: relative;\r\n  z-index: 3;\r\n}\r\n\r\n.page-header {\r\n  margin-bottom: 1.5rem;\r\n  padding: 1rem 0;\r\n  border-bottom: 2px solid #eaecf4;\r\n  background: linear-gradient(to right, #ffffff, #f8f9fc);\r\n}\r\n\r\n.page-title {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  padding: 0.5rem 1rem;\r\n  position: relative;\r\n}\r\n\r\n.page-title::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 4px;\r\n  height: 24px;\r\n  background: var(--primary-color);\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;EAESA,KAAK,EAAC;AAAS;;EAGbC,EAAE,EAAC;AAAS;;EAGVD,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAA+D;;EACpEA,KAAK,EAAC;AAAY;;EAGnBA,KAAK,EAAC;AAAkB;;;;;;uBAf3CE,YAAA,CAsBqBC,6BAAA;IAtBAC,MAAM,EAAEC,KAAA,CAAAD;EAAM;sBACjC,MAoBM,CApBNE,mBAAA,CAoBM,OApBNC,UAoBM,GAnBJC,YAAA,CAAYC,mBAAA,GAEZH,mBAAA,CAgBM,OAhBNI,UAgBM,GAfJF,YAAA,CAA+BG,iBAAA;MAAvBX,KAAK,EAAC;IAAc,IAE5BM,mBAAA,CAYM,OAZNM,UAYM,GAXJN,mBAAA,CAUM,OAVNO,UAUM,GATJP,mBAAA,CAQM,OARNQ,UAQM,GAPJR,mBAAA,CAMM,OANNS,UAMM,GALJT,mBAAA,CAEM,OAFNU,UAEM,GADJV,mBAAA,CAAwD,MAAxDW,UAAwD,EAAAC,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,GAGlDf,mBAAA,CAAmD,OAAnDgB,UAAmD,GAArBd,YAAA,CAAee,sBAAA,E"}]}