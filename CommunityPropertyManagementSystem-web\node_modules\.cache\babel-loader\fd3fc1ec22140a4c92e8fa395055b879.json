{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue?vue&type=template&id=c1f1971a&scoped=true", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue", "mtime": 1749135914930}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749133882132}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\@vue\\cli-service\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749133881456}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "id", "_createBlock", "_component_el_config_provider", "locale", "$data", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_LeftMenu", "_hoisted_2", "_component_Header", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "$route", "meta", "title", "_hoisted_9", "_component_router_view"], "sources": ["I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n  <el-config-provider :locale=\"locale\">\r\n    <div class=\"wrapper\" style=\"\">\r\n      <LeftMenu />\r\n\r\n      <div id=\"content\">\r\n        <Header class=\"header-fixed\" />\r\n\r\n        <div class=\"main-content\">\r\n          <div class=\"container-fluid p-4\">\r\n            <div class=\"page-container\">\r\n              <div class=\"content-container\">\r\n                <div class=\"page-header d-flex justify-content-between align-items-center\">\r\n                  <h5 class=\"page-title\">{{ this.$route.meta.title }}</h5>\r\n                </div>\r\n\r\n                <div class=\"table-responsive\"><router-view /></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-config-provider>\r\n</template>\r\n\r\n<script>\r\nimport Header from '../components/Header';\r\nimport LeftMenu from '../components/LeftMenu';\r\nimport { ElConfigProvider } from 'element-plus';\r\nimport zhCn from 'element-plus/lib/locale/lang/zh-cn';\r\nimport $ from 'jquery';\r\nexport default {\r\n  name: 'MainLayout',\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n  },\r\n\r\n  methods: {},\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n@import url(../assets/css/ht_bootstrap.min.css);\r\n@import url(../assets/css/ht_bootstrap-icons.css);\r\n@import url(../assets/css/ht_style.css);\r\n\r\n.wrapper {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  position: relative;\r\n}\r\n\r\n#content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  position: relative;\r\n}\r\n\r\n.header-fixed {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  left: var(--sidebar-width);\r\n  z-index: 100;\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  padding-top: var(--header-height);\r\n  overflow-y: auto;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.content-container {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.table-responsive {\r\n  position: relative;\r\n  z-index: 3;\r\n}\r\n\r\n.page-header {\r\n  margin-bottom: 1.5rem;\r\n  padding: 1rem 0;\r\n  border-bottom: 2px solid #eaecf4;\r\n  background: linear-gradient(to right, #ffffff, #f8f9fc);\r\n}\r\n\r\n.page-title {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  margin: 0;\r\n  padding: 0.5rem 1rem;\r\n  position: relative;\r\n}\r\n\r\n.page-title::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 4px;\r\n  height: 24px;\r\n  background: var(--primary-color);\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;EAESA,KAAK,EAAC,SAAS;EAACC,KAAQ,EAAR;;;EAGdC,EAAE,EAAC;AAAS;;EAGVF,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAA+D;;EACpEA,KAAK,EAAC;AAAY;;EAGnBA,KAAK,EAAC;AAAkB;;;;;;uBAf3CG,YAAA,CAsBqBC,6BAAA;IAtBAC,MAAM,EAAEC,KAAA,CAAAD;EAAM;sBACjC,MAoBM,CApBNE,mBAAA,CAoBM,OApBNC,UAoBM,GAnBJC,YAAA,CAAYC,mBAAA,GAEZH,mBAAA,CAgBM,OAhBNI,UAgBM,GAfJF,YAAA,CAA+BG,iBAAA;MAAvBZ,KAAK,EAAC;IAAc,IAE5BO,mBAAA,CAYM,OAZNM,UAYM,GAXJN,mBAAA,CAUM,OAVNO,UAUM,GATJP,mBAAA,CAQM,OARNQ,UAQM,GAPJR,mBAAA,CAMM,OANNS,UAMM,GALJT,mBAAA,CAEM,OAFNU,UAEM,GADJV,mBAAA,CAAwD,MAAxDW,UAAwD,EAAAC,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,GAGlDf,mBAAA,CAAmD,OAAnDgB,UAAmD,GAArBd,YAAA,CAAee,sBAAA,E"}]}