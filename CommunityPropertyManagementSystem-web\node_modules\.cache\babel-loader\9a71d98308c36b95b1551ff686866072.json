{"remainingRequest": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\router\\index.js", "dependencies": [{"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\src\\router\\index.js", "mtime": 1749135805998}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\babel.config.js", "mtime": 1749133488000}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749133880188}, {"path": "I:\\product4\\00453CommunityPropertyManagementSystem\\CommunityPropertyManagementSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749133881174}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["createRouter", "createWebHistory", "routes", "path", "name", "component", "meta", "requireAuth", "redirect", "children", "title", "requiresAuth", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "sessionStorage", "removeItem", "currentUser", "getItem", "console", "log"], "sources": ["I:/product4/00453CommunityPropertyManagementSystem/CommunityPropertyManagementSystem-web/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\n\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'Login',\r\n    component: () => import('../views/Login'),\r\n    meta: {\r\n      requireAuth: false\r\n    }\r\n  },\r\n\r\n  {\r\n    path: '/main',\r\n    name: 'Main',\r\n    component: () => import('../views/Main'),\r\n    redirect: \"/home\",\r\n    children: [\r\n      {\r\n        path: '/home',\r\n        name: 'Home',\r\n        component: () => import('../views/admin/Home'),\r\n        meta: {\r\n          requireAuth: true,title:'首页'\r\n        }\r\n\r\n      },\r\n     \r\n      {\r\n      path: '/usersAdd',\r\n      name: 'UsersAdd',\r\n      component: () => import('../views/admin/users/UsersAdd'),\r\n      meta: { requiresAuth: true,title: '用户添加' }\r\n    },\r\n {\r\n      path: '/usersEdit',\r\n      name: 'UsersEdit',\r\n      component: () => import('../views/admin/users/UsersEdit'),\r\n      meta: { requiresAuth: true,title: '用户修改' }\r\n    },\r\n {\r\n      path: '/usersManage',\r\n      name: 'UsersManage',\r\n      component: () => import('../views/admin/users/UsersManage'),\r\n      meta: { requiresAuth: true,title: '用户管理' }\r\n    },\r\n{\r\n      path: '/usersManage2',\r\n      name: 'UsersManage2',\r\n      component: () => import('../views/admin/users/UsersManage2'),\r\n      meta: { requiresAuth: true,title: '用户列表' }\r\n    },\r\n{\r\n    path: '/usersDetail',\r\n    name: 'UsersDetail',\r\n    component: () => import('../views/admin/users/UsersDetail'),\r\n    meta: { requiresAuth: true,title: '用户详情' }\r\n  },\r\n{\r\n      path: '/usersInfo',\r\n      name: 'UsersInfo',\r\n      component: () => import('../views/admin/users/UsersInfo'),\r\n      meta: { requiresAuth: true,title: '修改个人信息' }\r\n    },\r\n{\r\n      path: '/announcementsAdd',\r\n      name: 'AnnouncementsAdd',\r\n      component: () => import('../views/admin/announcements/AnnouncementsAdd'),\r\n      meta: { requiresAuth: true,title: '公告添加' }\r\n    },\r\n {\r\n      path: '/announcementsEdit',\r\n      name: 'AnnouncementsEdit',\r\n      component: () => import('../views/admin/announcements/AnnouncementsEdit'),\r\n      meta: { requiresAuth: true,title: '公告修改' }\r\n    },\r\n {\r\n      path: '/announcementsManage',\r\n      name: 'AnnouncementsManage',\r\n      component: () => import('../views/admin/announcements/AnnouncementsManage'),\r\n      meta: { requiresAuth: true,title: '公告管理' }\r\n    },\r\n{\r\n      path: '/announcementsManage2',\r\n      name: 'AnnouncementsManage2',\r\n      component: () => import('../views/admin/announcements/AnnouncementsManage2'),\r\n      meta: { requiresAuth: true,title: '公告列表' }\r\n    },\r\n{\r\n    path: '/announcementsDetail',\r\n    name: 'AnnouncementsDetail',\r\n    component: () => import('../views/admin/announcements/AnnouncementsDetail'),\r\n    meta: { requiresAuth: true,title: '公告详情' }\r\n  },\r\n{\r\n      path: '/complaintsAdd',\r\n      name: 'ComplaintsAdd',\r\n      component: () => import('../views/admin/complaints/ComplaintsAdd'),\r\n      meta: { requiresAuth: true,title: '提交投诉建议' }\r\n    },\r\n {\r\n      path: '/complaintsEdit',\r\n      name: 'ComplaintsEdit',\r\n      component: () => import('../views/admin/complaints/ComplaintsEdit'),\r\n      meta: { requiresAuth: true,title: '投诉建议修改' }\r\n    },\r\n {\r\n      path: '/complaintsManage',\r\n      name: 'ComplaintsManage',\r\n      component: () => import('../views/admin/complaints/ComplaintsManage'),\r\n      meta: { requiresAuth: true,title: '投诉建议管理' }\r\n    },\r\n{\r\n      path: '/complaintsManage2',\r\n      name: 'ComplaintsManage2',\r\n      component: () => import('../views/admin/complaints/ComplaintsManage2'),\r\n      meta: { requiresAuth: true,title: '投诉建议列表' }\r\n    },\r\n{\r\n    path: '/complaintsDetail',\r\n    name: 'ComplaintsDetail',\r\n    component: () => import('../views/admin/complaints/ComplaintsDetail'),\r\n    meta: { requiresAuth: true,title: '投诉建议详情' }\r\n  },\r\n{\r\n      path: '/houseAdd',\r\n      name: 'HouseAdd',\r\n      component: () => import('../views/admin/house/HouseAdd'),\r\n      meta: { requiresAuth: true,title: '房屋信息添加' }\r\n    },\r\n {\r\n      path: '/houseEdit',\r\n      name: 'HouseEdit',\r\n      component: () => import('../views/admin/house/HouseEdit'),\r\n      meta: { requiresAuth: true,title: '房屋信息修改' }\r\n    },\r\n {\r\n      path: '/houseManage',\r\n      name: 'HouseManage',\r\n      component: () => import('../views/admin/house/HouseManage'),\r\n      meta: { requiresAuth: true,title: '房屋信息管理' }\r\n      },\r\n{\r\n      path: '/houseManage2',\r\n      name: 'HouseManage2',\r\n      component: () => import('../views/admin/house/HouseManage2'),\r\n      meta: { requiresAuth: true,title: '房屋信息列表' }\r\n    },\r\n{\r\n    path: '/houseDetail',\r\n    name: 'HouseDetail',\r\n    component: () => import('../views/admin/house/HouseDetail'),\r\n    meta: { requiresAuth: true,title: '房屋信息详情' }\r\n  },\r\n{\r\n      path: '/propertystaffAdd',\r\n      name: 'PropertystaffAdd',\r\n      component: () => import('../views/admin/propertystaff/PropertystaffAdd'),\r\n      meta: { requiresAuth: true,title: '工作人员添加' }\r\n    },\r\n {\r\n      path: '/propertystaffEdit',\r\n      name: 'PropertystaffEdit',\r\n      component: () => import('../views/admin/propertystaff/PropertystaffEdit'),\r\n      meta: { requiresAuth: true,title: '工作人员修改' }\r\n    },\r\n {\r\n      path: '/propertystaffManage',\r\n      name: 'PropertystaffManage',\r\n      component: () => import('../views/admin/propertystaff/PropertystaffManage'),\r\n      meta: { requiresAuth: true,title: '工作人员管理' }\r\n      },\r\n{\r\n      path: '/propertystaffManage2',\r\n      name: 'PropertystaffManage2',\r\n      component: () => import('../views/admin/propertystaff/PropertystaffManage2'),\r\n      meta: { requiresAuth: true,title: '工作人员列表' }\r\n    },\r\n{\r\n    path: '/propertystaffDetail',\r\n    name: 'PropertystaffDetail',\r\n    component: () => import('../views/admin/propertystaff/PropertystaffDetail'),\r\n    meta: { requiresAuth: true,title: '工作人员详情' }\r\n  },\r\n{\r\n      path: '/propertystaffInfo',\r\n      name: 'PropertystaffInfo',\r\n      component: () => import('../views/admin/propertystaff/PropertystaffInfo'),\r\n      meta: { requiresAuth: true,title: '修改个人信息' }\r\n    },\r\n{\r\n      path: '/expenseinfoAdd',\r\n      name: 'ExpenseinfoAdd',\r\n      component: () => import('../views/admin/expenseinfo/ExpenseinfoAdd'),\r\n      meta: { requiresAuth: true,title: '费用信息添加' }\r\n    },\r\n {\r\n      path: '/expenseinfoEdit',\r\n      name: 'ExpenseinfoEdit',\r\n      component: () => import('../views/admin/expenseinfo/ExpenseinfoEdit'),\r\n      meta: { requiresAuth: true,title: '费用信息修改' }\r\n    },\r\n {\r\n      path: '/expenseinfoManage',\r\n      name: 'ExpenseinfoManage',\r\n      component: () => import('../views/admin/expenseinfo/ExpenseinfoManage'),\r\n      meta: { requiresAuth: true,title: '费用信息管理' }\r\n    },\r\n{\r\n      path: '/expenseinfoManage2',\r\n      name: 'ExpenseinfoManage2',\r\n      component: () => import('../views/admin/expenseinfo/ExpenseinfoManage2'),\r\n      meta: { requiresAuth: true,title: '费用信息列表' }\r\n    },\r\n{\r\n    path: '/expenseinfoDetail',\r\n    name: 'ExpenseinfoDetail',\r\n    component: () => import('../views/admin/expenseinfo/ExpenseinfoDetail'),\r\n    meta: { requiresAuth: true,title: '费用信息详情' }\r\n  },\r\n\r\n     {\r\n          path: '/password',\r\n          name: 'Password',\r\n          component: () => import('../views/admin/system/Password'),\r\n          meta: {\r\n            requireAuth: true,title:'修改密码'\r\n          }\r\n     },\r\n    ]\r\n  },\r\n    \r\n  \r\n]\r\n\r\n\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes\r\n})\r\n\r\n\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n  if (to.path == '/') {\r\n    sessionStorage.removeItem('userLname');\r\n    sessionStorage.removeItem('role');\r\n  }\r\n  let currentUser = sessionStorage.getItem('userLname');\r\n  console.log(to + \"  to.meta.requireAuth\");\r\n\r\n  if (to.meta.requireAuth) {\r\n    if (!currentUser && to.path != '/login') {\r\n      next({ path: '/' });\r\n    } else {\r\n      next();\r\n    }\r\n  } else {\r\n\r\n    next();\r\n  }\r\n})\r\n\r\nexport default router\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAE3D,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC;EACzCC,IAAI,EAAE;IACJC,WAAW,EAAE;EACf;AACF,CAAC,EAED;EACEJ,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC;EACxCG,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC;IAC9CC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MAACG,KAAK,EAAC;IAC1B;EAEF,CAAC,EAED;IACAP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACMP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC;IAC5DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACIP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACH;IACMP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACL;IACMP,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC;IACxEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC;IACzEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC;IAC3EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACMP,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC;IAC5EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACIP,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC;IAC3EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACH;IACMP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC;IAClEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACJ;IACKP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC;IACnEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACJ;IACKP,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC;IACrEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACL;IACMP,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6CAA6C,CAAC;IACtEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACL;IACIP,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC;IACrEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACH;IACMP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACJ;IACKP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACJ;IACKP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC3C,CAAC,EACP;IACMP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC;IAC5DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACL;IACIP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACH;IACMP,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC;IACxEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACJ;IACKP,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC;IACzEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACJ;IACKP,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC;IAC3EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC3C,CAAC,EACP;IACMP,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC;IAC5EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACL;IACIP,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC;IAC3EC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACH;IACMP,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC;IACzEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACL;IACMP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC;IACpEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACJ;IACKP,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC;IACrEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACJ;IACKP,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC;IACvEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACL;IACMP,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC;IACxEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EACL;IACIP,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC;IACvEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAS;EAC7C,CAAC,EAEE;IACKP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MAACG,KAAK,EAAC;IAC1B;EACL,CAAC;AAEJ,CAAC,CAGF;AAID,MAAME,MAAM,GAAGZ,YAAY,CAAC;EAC1Ba,OAAO,EAAEZ,gBAAgB,CAACa,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/Cd;AACF,CAAC,CAAC;AAIFU,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,IAAIF,EAAE,CAACf,IAAI,IAAI,GAAG,EAAE;IAClBkB,cAAc,CAACC,UAAU,CAAC,WAAW,CAAC;IACtCD,cAAc,CAACC,UAAU,CAAC,MAAM,CAAC;EACnC;EACA,IAAIC,WAAW,GAAGF,cAAc,CAACG,OAAO,CAAC,WAAW,CAAC;EACrDC,OAAO,CAACC,GAAG,CAACR,EAAE,GAAG,uBAAuB,CAAC;EAEzC,IAAIA,EAAE,CAACZ,IAAI,CAACC,WAAW,EAAE;IACvB,IAAI,CAACgB,WAAW,IAAIL,EAAE,CAACf,IAAI,IAAI,QAAQ,EAAE;MACvCiB,IAAI,CAAC;QAAEjB,IAAI,EAAE;MAAI,CAAC,CAAC;IACrB,CAAC,MAAM;MACLiB,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IAELA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeR,MAAM"}]}