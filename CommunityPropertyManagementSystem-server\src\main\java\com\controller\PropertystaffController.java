package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/propertystaff")
public class PropertystaffController{
	
	@Resource
	private PropertystaffService propertystaffService;
	
	//工作人员列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Propertystaff>> list(@RequestBody Propertystaff propertystaff, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = propertystaffService.getCount(propertystaff);
		//获取当前页记录
		List<Propertystaff> propertystaffList = propertystaffService.queryPropertystaffList(propertystaff, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(propertystaffList, counts, page_count);
	}
        
	//添加工作人员
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Propertystaff propertystaff, HttpServletRequest req) throws Exception {
		try {
			//判断用户名是否存在
            Propertystaff propertystaff1 = new Propertystaff();
            propertystaff1.setPlname(propertystaff.getPlname());
            List<Propertystaff> propertystaffList = propertystaffService.queryPropertystaffList(propertystaff1, null);
            if (propertystaffList.size() > 0) {
                return Response.error(201, "用户名已存在，请重新输入");
            }
            else
            {
                propertystaffService.insertPropertystaff(propertystaff); //添加
            }
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除工作人员
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			propertystaffService.deletePropertystaff(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改工作人员
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Propertystaff propertystaff, HttpServletRequest req) throws Exception {
		try {
			propertystaffService.updatePropertystaff(propertystaff); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回工作人员详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Propertystaff propertystaff=propertystaffService.queryPropertystaffById(id); //根据ID查询
			return Response.success(propertystaff);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
	//登录
    @ResponseBody
    @PostMapping(value = "/login")
    @CrossOrigin
    public Response login(@RequestBody Propertystaff propertystaff, HttpServletRequest request) throws Exception {

        try {
            List<Propertystaff> propertystaffList = propertystaffService.queryPropertystaffList(propertystaff, null);   //查询

            //判断是否有数据
			if (propertystaffList.size() > 0) {
				if(propertystaffList.get(0).getStatus().equals("待审核")){
					return Response.error(201, "登录失败，您还未通过审核，请等待管理员审核");
				}
				else if(propertystaffList.get(0).getStatus().equals("不通过")){
					return Response.error(201, "登录失败，您的账号未通过审核，请重新注册");
				}
                return Response.success(propertystaffList.get(0));  //登录成功
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error();
        }
        return Response.error(201, "用户名或密码错误");
    }
	//修改密码
    @ResponseBody
    @PostMapping(value = "/updatePwd")
    @CrossOrigin
    public Response updatePwd(@RequestBody Propertystaff propertystaff, HttpServletRequest req) throws Exception {
        try {
            Propertystaff propertystaff1 = propertystaffService.queryPropertystaffById(propertystaff.getPid()); //根据ID查询
            //判断原密码是否正确
            if (!propertystaff1.getPword().equals(propertystaff.getBy1())) {
                return Response.error(201, "原密码错误");
            } else {
                propertystaff1.setPword(propertystaff.getBy2());  //新密码
                propertystaffService.updatePropertystaff(propertystaff1); //修改新密码
            }

        } catch (Exception e) {
            return Response.error();
        }
        return Response.success();
    }

}

