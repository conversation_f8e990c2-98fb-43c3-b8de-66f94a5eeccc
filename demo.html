<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小区物业管理系统 - 欢迎页面演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .welcome-section {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .welcome-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .welcome-info {
            font-size: 1.2rem;
            margin: 0;
            opacity: 0.9;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            height: 120px;
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
        }
        
        .stat-icon {
            font-size: 3rem;
            margin-right: 20px;
            color: var(--card-color);
            opacity: 0.8;
        }
        
        .stat-content h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0 0 5px 0;
            color: #2c3e50;
        }
        
        .stat-content p {
            font-size: 1rem;
            color: #7f8c8d;
            margin: 0;
            font-weight: 500;
        }
        
        .user-card {
            --card-color: #3498db;
            --card-color-light: #5dade2;
        }
        
        .staff-card {
            --card-color: #2ecc71;
            --card-color-light: #58d68d;
        }
        
        .announcement-card {
            --card-color: #f39c12;
            --card-color-light: #f8c471;
        }
        
        .complaint-card {
            --card-color: #e74c3c;
            --card-color-light: #ec7063;
        }
        
        .expense-card {
            --card-color: #9b59b6;
            --card-color-light: #bb8fce;
        }
        
        .house-card {
            --card-color: #1abc9c;
            --card-color-light: #5dccb4;
        }
        
        .role-section {
            margin-bottom: 40px;
        }
        
        .role-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="welcome-section">
            <h1 class="welcome-title">欢迎使用小区物业管理系统</h1>
            <p class="welcome-info">
                账号：<b style="color: #ffd700;">admin</b>，
                身份：<b style="color: #ffd700;">管理员</b>
            </p>
        </div>

        <!-- 管理员视图 -->
        <div class="role-section">
            <h2 class="role-title">管理员登录后显示</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="stat-card user-card">
                        <div class="stat-icon">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <div class="stat-content">
                            <h3>156</h3>
                            <p>用户数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="stat-card staff-card">
                        <div class="stat-icon">
                            <i class="bi bi-person-badge-fill"></i>
                        </div>
                        <div class="stat-content">
                            <h3>23</h3>
                            <p>工作人员数量</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工作人员视图 -->
        <div class="role-section">
            <h2 class="role-title">工作人员登录后显示</h2>
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card user-card">
                        <div class="stat-icon">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <div class="stat-content">
                            <h3>156</h3>
                            <p>用户数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card announcement-card">
                        <div class="stat-icon">
                            <i class="bi bi-megaphone-fill"></i>
                        </div>
                        <div class="stat-content">
                            <h3>42</h3>
                            <p>公告信息数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card complaint-card">
                        <div class="stat-icon">
                            <i class="bi bi-chat-square-text-fill"></i>
                        </div>
                        <div class="stat-content">
                            <h3>18</h3>
                            <p>投诉建议数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card expense-card">
                        <div class="stat-icon">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="stat-content">
                            <h3>89</h3>
                            <p>费用信息数量</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户视图 -->
        <div class="role-section">
            <h2 class="role-title">用户登录后显示</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card house-card">
                        <div class="stat-icon">
                            <i class="bi bi-house-fill"></i>
                        </div>
                        <div class="stat-content">
                            <h3>2</h3>
                            <p>房屋信息数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card expense-card">
                        <div class="stat-icon">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="stat-content">
                            <h3>5</h3>
                            <p>费用信息数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card complaint-card">
                        <div class="stat-icon">
                            <i class="bi bi-chat-square-text-fill"></i>
                        </div>
                        <div class="stat-content">
                            <h3>3</h3>
                            <p>投诉建议数量</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
