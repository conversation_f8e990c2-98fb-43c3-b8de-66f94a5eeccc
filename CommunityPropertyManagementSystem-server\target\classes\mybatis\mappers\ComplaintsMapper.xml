<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.ComplaintsMapper">
	<select id="findComplaintsList"  resultType="Complaints">
		select * from complaints 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Complaints">
	    select  *  
        from complaints a  	
		<where>
      		<if test="cid != null and cid !=0 ">
		    and a.cid = #{cid}
		</if>
		<if test="ctype != null and ctype != ''">
		    and a.ctype = #{ctype}
		</if>
		<if test="ctitle != null and ctitle != ''">
		    and a.ctitle = #{ctitle}
		</if>
		<if test="uid != null and uid !=0 ">
		    and a.uid = #{uid}
		</if>
		<if test="ctime != null and ctime != ''">
		    and a.ctime = #{ctime}
		</if>
		<if test="cstatus != null and cstatus != ''">
		    and a.cstatus = #{cstatus}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} cid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from complaints a  
		<where>
      		<if test="cid != null and cid !=0 ">
		    and a.cid = #{cid}
		</if>
		<if test="ctype != null and ctype != ''">
		    and a.ctype = #{ctype}
		</if>
		<if test="ctitle != null and ctitle != ''">
		    and a.ctitle = #{ctitle}
		</if>
		<if test="uid != null and uid !=0 ">
		    and a.uid = #{uid}
		</if>
		<if test="ctime != null and ctime != ''">
		    and a.ctime = #{ctime}
		</if>
		<if test="cstatus != null and cstatus != ''">
		    and a.cstatus = #{cstatus}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryComplaintsById" parameterType="int" resultType="Complaints">
    select  *  
     from complaints a  	 where a.cid=#{value}
  </select>
 
	<insert id="insertComplaints" useGeneratedKeys="true" keyProperty="cid" parameterType="Complaints">
    insert into complaints
    (ctype,ctitle,ccontent,uid,ctime,cstatus,cresult)
    values
    (#{ctype},#{ctitle},#{ccontent},#{uid},now(),#{cstatus},#{cresult});
  </insert>
	
	<update id="updateComplaints" parameterType="Complaints" >
    update complaints 
    <set>
		<if test="ctype != null and ctype != ''">
		    ctype = #{ctype},
		</if>
		<if test="ctitle != null and ctitle != ''">
		    ctitle = #{ctitle},
		</if>
		<if test="ccontent != null and ccontent != ''">
		    ccontent = #{ccontent},
		</if>
		<if test="uid != null ">
		    uid = #{uid},
		</if>
		<if test="ctime != null and ctime != ''">
		    ctime = #{ctime},
		</if>
		<if test="cstatus != null and cstatus != ''">
		    cstatus = #{cstatus},
		</if>
		<if test="cresult != null and cresult != ''">
		    cresult = #{cresult},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="cid != null or cid != ''">
      cid=#{cid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteComplaints" parameterType="int">
    delete from  complaints where cid=#{value}
  </delete>

	
	
</mapper>

 
